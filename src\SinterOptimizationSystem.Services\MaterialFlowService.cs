using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Services
{
    /// <summary>
    /// 料流跟踪服务实现
    /// </summary>
    public class MaterialFlowService : IMaterialFlowService
    {
        private readonly ILogger<MaterialFlowService> _logger;
        private readonly IMaterialManagementService _materialService;
        private readonly List<MaterialFlow> _materialFlows; // 模拟数据存储
        private readonly Dictionary<string, decimal> _binLevels; // 料仓仓位信息

        public MaterialFlowService(ILogger<MaterialFlowService> logger, IMaterialManagementService materialService)
        {
            _logger = logger;
            _materialService = materialService;
            _materialFlows = new List<MaterialFlow>();
            _binLevels = new Dictionary<string, decimal>();
        }

        #region 料流基础管理

        public async Task<bool> CreateMaterialFlowAsync(MaterialFlow materialFlow)
        {
            try
            {
                _logger.LogInformation("创建新的料流批次: {BatchNumber}", materialFlow.BatchNumber);

                // 检查批次号是否重复
                if (_materialFlows.Any(f => f.BatchNumber == materialFlow.BatchNumber))
                {
                    _logger.LogWarning("批次号已存在: {BatchNumber}", materialFlow.BatchNumber);
                    return false;
                }

                materialFlow.Id = _materialFlows.Count + 1;
                materialFlow.CreatedAt = DateTime.Now;
                materialFlow.UpdatedAt = DateTime.Now;
                materialFlow.RemainingWeight = materialFlow.BatchWeight;
                materialFlow.Status = MaterialFlowStatus.Incoming;

                _materialFlows.Add(materialFlow);

                _logger.LogInformation("料流批次创建成功: {BatchNumber}", materialFlow.BatchNumber);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建料流批次失败: {BatchNumber}", materialFlow.BatchNumber);
                return false;
            }
        }

        public async Task<MaterialFlow?> GetMaterialFlowAsync(string batchNumber)
        {
            try
            {
                _logger.LogInformation("获取料流信息: {BatchNumber}", batchNumber);
                return await Task.FromResult(_materialFlows.FirstOrDefault(f => f.BatchNumber == batchNumber && !f.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取料流信息失败: {BatchNumber}", batchNumber);
                return null;
            }
        }

        public async Task<IEnumerable<MaterialFlow>> GetCurrentFlowsInBinAsync(string binNumber)
        {
            try
            {
                _logger.LogInformation("获取料仓当前料流: {BinNumber}", binNumber);
                return await Task.FromResult(_materialFlows.Where(f => 
                    f.BinNumber == binNumber && 
                    f.Status != MaterialFlowStatus.Completed && 
                    !f.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取料仓当前料流失败: {BinNumber}", binNumber);
                return Enumerable.Empty<MaterialFlow>();
            }
        }

        public async Task<IEnumerable<MaterialFlow>> GetMaterialFlowHistoryAsync(int materialId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("获取原料料流历史: {MaterialId}, {StartDate} - {EndDate}", materialId, startDate, endDate);
                return await Task.FromResult(_materialFlows.Where(f => 
                    f.MaterialId == materialId && 
                    f.IncomingTime >= startDate && 
                    f.IncomingTime <= endDate && 
                    !f.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取原料料流历史失败: {MaterialId}", materialId);
                return Enumerable.Empty<MaterialFlow>();
            }
        }

        public async Task<bool> UpdateFlowStatusAsync(string batchNumber, MaterialFlowStatus status)
        {
            try
            {
                _logger.LogInformation("更新料流状态: {BatchNumber}, {Status}", batchNumber, status);

                var flow = _materialFlows.FirstOrDefault(f => f.BatchNumber == batchNumber && !f.IsDeleted);
                if (flow == null)
                {
                    _logger.LogWarning("料流不存在: {BatchNumber}", batchNumber);
                    return false;
                }

                flow.Status = status;
                flow.UpdatedAt = DateTime.Now;

                // 根据状态更新相关时间
                switch (status)
                {
                    case MaterialFlowStatus.Feeding:
                        flow.StartFeedingTime = DateTime.Now;
                        break;
                    case MaterialFlowStatus.Completed:
                        flow.EndFeedingTime = DateTime.Now;
                        break;
                }

                _logger.LogInformation("料流状态更新成功: {BatchNumber}", batchNumber);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新料流状态失败: {BatchNumber}", batchNumber);
                return false;
            }
        }

        #endregion

        #region 灌仓管理

        public async Task<(bool IsSuccess, string BatchNumber)> StartBinFillingAsync(string binNumber, int materialId, decimal batchWeight, string? supplierBatchNumber = null)
        {
            try
            {
                _logger.LogInformation("开始灌仓: {BinNumber}, 原料ID: {MaterialId}, 批重: {BatchWeight}", binNumber, materialId, batchWeight);

                // 生成批次号
                var batchNumber = $"{binNumber}_{DateTime.Now:yyyyMMddHHmmss}";

                var materialFlow = new MaterialFlow
                {
                    BatchNumber = batchNumber,
                    MaterialId = materialId,
                    BinNumber = binNumber,
                    BatchWeight = batchWeight,
                    RemainingWeight = batchWeight,
                    IncomingTime = DateTime.Now,
                    Status = MaterialFlowStatus.Incoming,
                    SupplierBatchNumber = supplierBatchNumber
                };

                var result = await CreateMaterialFlowAsync(materialFlow);
                if (result)
                {
                    _logger.LogInformation("灌仓开始成功: {BatchNumber}", batchNumber);
                    return (true, batchNumber);
                }
                else
                {
                    return (false, string.Empty);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始灌仓失败: {BinNumber}", binNumber);
                return (false, string.Empty);
            }
        }

        public async Task<bool> CompleteBinFillingAsync(string batchNumber, decimal actualWeight)
        {
            try
            {
                _logger.LogInformation("完成灌仓: {BatchNumber}, 实际重量: {ActualWeight}", batchNumber, actualWeight);

                var flow = await GetMaterialFlowAsync(batchNumber);
                if (flow == null)
                {
                    _logger.LogWarning("料流不存在: {BatchNumber}", batchNumber);
                    return false;
                }

                flow.BatchWeight = actualWeight;
                flow.RemainingWeight = actualWeight;
                flow.Status = MaterialFlowStatus.Storing;
                flow.UpdatedAt = DateTime.Now;

                // 记录灌仓操作详情
                var detail = new MaterialFlowDetail
                {
                    MaterialFlowId = flow.Id,
                    OperationType = FlowOperationType.Incoming,
                    WeightBefore = 0,
                    WeightAfter = actualWeight,
                    WeightChange = actualWeight,
                    OperationTime = DateTime.Now,
                    IsAutomatic = false
                };

                flow.FlowDetails.Add(detail);

                _logger.LogInformation("灌仓完成: {BatchNumber}", batchNumber);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成灌仓失败: {BatchNumber}", batchNumber);
                return false;
            }
        }

        public async Task<(int FillingCount, decimal TotalWeight, TimeSpan TotalTime)> GetFillingStatsAsync(string binNumber, DateTime date)
        {
            try
            {
                _logger.LogInformation("获取灌仓统计: {BinNumber}, {Date}", binNumber, date);

                var flows = _materialFlows.Where(f => 
                    f.BinNumber == binNumber && 
                    f.IncomingTime.Date == date.Date && 
                    !f.IsDeleted).ToList();

                var fillingCount = flows.Count;
                var totalWeight = flows.Sum(f => f.BatchWeight);
                var totalTime = flows.Aggregate(TimeSpan.Zero, (sum, f) => sum + ((f.EndFeedingTime ?? DateTime.Now) - f.IncomingTime));

                return await Task.FromResult((fillingCount, totalWeight, totalTime));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取灌仓统计失败: {BinNumber}", binNumber);
                return (0, 0, TimeSpan.Zero);
            }
        }

        #endregion

        #region 下料批次跟踪

        public async Task<bool> StartFeedingAsync(string batchNumber, string feedingPortNumber, decimal targetRate)
        {
            try
            {
                _logger.LogInformation("开始下料: {BatchNumber}, 下料口: {FeedingPortNumber}, 目标速率: {TargetRate}", 
                    batchNumber, feedingPortNumber, targetRate);

                var flow = await GetMaterialFlowAsync(batchNumber);
                if (flow == null)
                {
                    _logger.LogWarning("料流不存在: {BatchNumber}", batchNumber);
                    return false;
                }

                flow.Status = MaterialFlowStatus.Feeding;
                flow.StartFeedingTime = DateTime.Now;
                flow.FeedingRate = targetRate;
                flow.UpdatedAt = DateTime.Now;

                // 记录下料开始操作
                var detail = new MaterialFlowDetail
                {
                    MaterialFlowId = flow.Id,
                    OperationType = FlowOperationType.Feeding,
                    WeightBefore = flow.RemainingWeight,
                    WeightAfter = flow.RemainingWeight,
                    WeightChange = 0,
                    FeedingRate = targetRate,
                    FeedingPortNumber = feedingPortNumber,
                    OperationTime = DateTime.Now,
                    IsAutomatic = true
                };

                flow.FlowDetails.Add(detail);

                _logger.LogInformation("下料开始成功: {BatchNumber}", batchNumber);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始下料失败: {BatchNumber}", batchNumber);
                return false;
            }
        }

        public async Task<bool> RecordFeedingOperationAsync(string batchNumber, FlowOperationType operationType, decimal weightChange, string? operationReason = null)
        {
            try
            {
                _logger.LogInformation("记录下料操作: {BatchNumber}, 操作类型: {OperationType}, 重量变化: {WeightChange}", 
                    batchNumber, operationType, weightChange);

                var flow = await GetMaterialFlowAsync(batchNumber);
                if (flow == null)
                {
                    _logger.LogWarning("料流不存在: {BatchNumber}", batchNumber);
                    return false;
                }

                var weightBefore = flow.RemainingWeight;
                var weightAfter = weightBefore - Math.Abs(weightChange); // 下料为减少重量

                // 更新剩余重量
                flow.RemainingWeight = Math.Max(0, weightAfter);
                flow.CumulativeFeedingAmount += Math.Abs(weightChange);
                flow.UpdatedAt = DateTime.Now;

                // 记录操作详情
                var detail = new MaterialFlowDetail
                {
                    MaterialFlowId = flow.Id,
                    OperationType = operationType,
                    WeightBefore = weightBefore,
                    WeightAfter = weightAfter,
                    WeightChange = -Math.Abs(weightChange), // 负值表示减少
                    FeedingRate = flow.FeedingRate,
                    OperationTime = DateTime.Now,
                    OperationReason = operationReason,
                    IsAutomatic = operationType == FlowOperationType.Feeding
                };

                flow.FlowDetails.Add(detail);

                // 检查是否完成
                if (flow.RemainingWeight <= 0.1m)
                {
                    flow.Status = MaterialFlowStatus.Completed;
                    flow.EndFeedingTime = DateTime.Now;
                }

                _logger.LogInformation("下料操作记录成功: {BatchNumber}", batchNumber);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录下料操作失败: {BatchNumber}", batchNumber);
                return false;
            }
        }

        public async Task<bool> CompleteFeedingAsync(string batchNumber)
        {
            try
            {
                _logger.LogInformation("完成下料: {BatchNumber}", batchNumber);

                var flow = await GetMaterialFlowAsync(batchNumber);
                if (flow == null)
                {
                    _logger.LogWarning("料流不存在: {BatchNumber}", batchNumber);
                    return false;
                }

                flow.Status = MaterialFlowStatus.Completed;
                flow.EndFeedingTime = DateTime.Now;
                flow.RemainingWeight = 0;
                flow.UpdatedAt = DateTime.Now;

                _logger.LogInformation("下料完成: {BatchNumber}", batchNumber);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成下料失败: {BatchNumber}", batchNumber);
                return false;
            }
        }

        public async Task<(decimal Progress, decimal RemainingWeight, TimeSpan EstimatedTime)> GetFeedingProgressAsync(string batchNumber)
        {
            try
            {
                _logger.LogInformation("获取下料进度: {BatchNumber}", batchNumber);

                var flow = await GetMaterialFlowAsync(batchNumber);
                if (flow == null)
                {
                    return (0, 0, TimeSpan.Zero);
                }

                var progress = flow.BatchWeight > 0 ? (flow.BatchWeight - flow.RemainingWeight) / flow.BatchWeight * 100 : 0;
                var estimatedTime = TimeSpan.Zero;

                if (flow.FeedingRate.HasValue && flow.FeedingRate > 0)
                {
                    var remainingHours = flow.RemainingWeight / flow.FeedingRate.Value;
                    estimatedTime = TimeSpan.FromHours((double)remainingHours);
                }

                return await Task.FromResult((progress, flow.RemainingWeight, estimatedTime));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取下料进度失败: {BatchNumber}", batchNumber);
                return (0, 0, TimeSpan.Zero);
            }
        }

        #endregion

        #region 动态定位

        public async Task<MaterialFlow?> LocateCurrentBatchAsync(string binNumber)
        {
            try
            {
                _logger.LogInformation("定位当前使用的料批: {BinNumber}", binNumber);

                // 查找正在下料的批次
                var currentBatch = _materialFlows
                    .Where(f => f.BinNumber == binNumber && f.Status == MaterialFlowStatus.Feeding && !f.IsDeleted)
                    .OrderBy(f => f.StartFeedingTime)
                    .FirstOrDefault();

                if (currentBatch == null)
                {
                    // 如果没有正在下料的，查找储存中的批次
                    currentBatch = _materialFlows
                        .Where(f => f.BinNumber == binNumber && f.Status == MaterialFlowStatus.Storing && !f.IsDeleted)
                        .OrderBy(f => f.IncomingTime)
                        .FirstOrDefault();
                }

                return await Task.FromResult(currentBatch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定位当前料批失败: {BinNumber}", binNumber);
                return null;
            }
        }

        public async Task<DateTime?> PredictBatchCompletionTimeAsync(string batchNumber, decimal currentRate)
        {
            try
            {
                _logger.LogInformation("预测料批消耗时间: {BatchNumber}, 当前速率: {CurrentRate}", batchNumber, currentRate);

                var flow = await GetMaterialFlowAsync(batchNumber);
                if (flow == null || currentRate <= 0)
                {
                    return null;
                }

                var remainingHours = flow.RemainingWeight / currentRate;
                var completionTime = DateTime.Now.AddHours((double)remainingHours);

                return await Task.FromResult(completionTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预测料批消耗时间失败: {BatchNumber}", batchNumber);
                return null;
            }
        }

        public async Task<(decimal Level, decimal Capacity, decimal UsedCapacity)> GetBinLevelInfoAsync(string binNumber)
        {
            try
            {
                _logger.LogInformation("获取料仓仓位信息: {BinNumber}", binNumber);

                var level = _binLevels.GetValueOrDefault(binNumber, 0);
                var capacity = 1000m; // 假设料仓容量为1000吨
                var usedCapacity = level / 100 * capacity; // 假设level为百分比

                return await Task.FromResult((level, capacity, usedCapacity));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取料仓仓位信息失败: {BinNumber}", binNumber);
                return (0, 0, 0);
            }
        }

        public async Task<bool> UpdateBinLevelAsync(string binNumber, decimal level)
        {
            try
            {
                _logger.LogInformation("更新仓位高度: {BinNumber}, {Level}", binNumber, level);

                _binLevels[binNumber] = level;

                // 更新相关料流的仓位信息
                var flows = _materialFlows.Where(f => f.BinNumber == binNumber && f.Status != MaterialFlowStatus.Completed).ToList();
                foreach (var flow in flows)
                {
                    flow.BinLevel = level;
                    flow.UpdatedAt = DateTime.Now;
                }

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新仓位高度失败: {BinNumber}", binNumber);
                return false;
            }
        }

        #endregion

        #region 其他接口实现（简化版本）

        public async Task<bool> MatchBatchCompositionAsync(string batchNumber, IEnumerable<MaterialComposition> compositions)
        {
            // 简化实现
            _logger.LogInformation("匹配料批成分: {BatchNumber}", batchNumber);
            return await Task.FromResult(true);
        }

        public async Task<IEnumerable<MaterialComposition>> GetBatchCompositionAsync(string batchNumber)
        {
            // 简化实现
            return await Task.FromResult(Enumerable.Empty<MaterialComposition>());
        }

        public async Task<Dictionary<CompositionType, decimal>> AnalyzeCompositionDeviationAsync(string batchNumber, IEnumerable<MaterialComposition> standardCompositions)
        {
            // 简化实现
            return await Task.FromResult(new Dictionary<CompositionType, decimal>());
        }

        public async Task<List<string>> DetectFlowAnomaliesAsync(string binNumber)
        {
            var anomalies = new List<string>();
            
            var flows = await GetCurrentFlowsInBinAsync(binNumber);
            foreach (var flow in flows)
            {
                if (flow.RemainingWeight < 0)
                    anomalies.Add($"批次 {flow.BatchNumber} 剩余重量异常");
                
                if (flow.Status == MaterialFlowStatus.Feeding && !flow.StartFeedingTime.HasValue)
                    anomalies.Add($"批次 {flow.BatchNumber} 下料状态异常");
            }

            return anomalies;
        }

        public async Task<string> HandleFeedingDeviationAsync(string batchNumber, decimal targetRate, decimal actualRate)
        {
            var deviation = Math.Abs(targetRate - actualRate) / targetRate * 100;
            
            if (deviation > 10)
                return $"下料速率偏差过大({deviation:F1}%)，建议检查设备状态";
            else if (deviation > 5)
                return $"下料速率轻微偏差({deviation:F1}%)，建议调整参数";
            else
                return "下料速率正常";
        }

        public async Task<bool> GenerateFlowAlarmAsync(string batchNumber, string alarmType, string message)
        {
            _logger.LogWarning("料流报警: {BatchNumber}, 类型: {AlarmType}, 消息: {Message}", batchNumber, alarmType, message);
            return await Task.FromResult(true);
        }

        public async Task<Dictionary<string, object>> GetFlowStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            var flows = _materialFlows.Where(f => f.IncomingTime >= startDate && f.IncomingTime <= endDate).ToList();
            
            return await Task.FromResult(new Dictionary<string, object>
            {
                ["TotalBatches"] = flows.Count,
                ["TotalWeight"] = flows.Sum(f => f.BatchWeight),
                ["CompletedBatches"] = flows.Count(f => f.Status == MaterialFlowStatus.Completed),
                ["AverageProcessingTime"] = flows.Where(f => f.EndFeedingTime.HasValue)
                    .Average(f => (f.EndFeedingTime!.Value - f.IncomingTime).TotalHours)
            });
        }

        public async Task<(decimal Efficiency, decimal AverageRate, decimal Utilization)> AnalyzeFlowEfficiencyAsync(string binNumber, int days = 7)
        {
            var startDate = DateTime.Now.AddDays(-days);
            var flows = _materialFlows.Where(f => f.BinNumber == binNumber && f.IncomingTime >= startDate).ToList();
            
            var efficiency = flows.Count > 0 ? flows.Count(f => f.Status == MaterialFlowStatus.Completed) / (decimal)flows.Count * 100 : 0;
            var averageRate = flows.Where(f => f.FeedingRate.HasValue).Average(f => f.FeedingRate!.Value);
            var utilization = 80m; // 简化计算

            return await Task.FromResult((efficiency, averageRate, utilization));
        }

        public async Task<Dictionary<DateTime, decimal>> GetFlowTrendAsync(int materialId, int days = 30)
        {
            var startDate = DateTime.Now.AddDays(-days);
            var flows = _materialFlows.Where(f => f.MaterialId == materialId && f.IncomingTime >= startDate).ToList();
            
            var trend = flows.GroupBy(f => f.IncomingTime.Date)
                .ToDictionary(g => g.Key, g => g.Sum(f => f.BatchWeight));

            return await Task.FromResult(trend);
        }

        public async Task<bool> RecordQualityTestAsync(string batchNumber, Dictionary<string, decimal> qualityParameters)
        {
            _logger.LogInformation("记录质量检测结果: {BatchNumber}", batchNumber);
            return await Task.FromResult(true);
        }

        public async Task<Dictionary<string, decimal>> GetQualityTrackingAsync(string batchNumber)
        {
            return await Task.FromResult(new Dictionary<string, decimal>());
        }

        public async Task<Dictionary<string, (decimal Mean, decimal StdDev, decimal Stability)>> AnalyzeQualityStabilityAsync(int materialId, int days = 30)
        {
            return await Task.FromResult(new Dictionary<string, (decimal Mean, decimal StdDev, decimal Stability)>());
        }

        #endregion
    }
}
