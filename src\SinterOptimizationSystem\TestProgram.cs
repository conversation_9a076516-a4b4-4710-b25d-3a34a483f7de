using System;
using System.Windows.Forms;

namespace SinterOptimizationSystem
{
    /// <summary>
    /// 测试程序入口 - 用于验证Windows Forms基本功能
    /// </summary>
    internal static class TestProgram
    {
        /// <summary>
        /// 测试程序的主入口点
        /// </summary>
        [STAThread]
        static void TestMain()
        {
            try
            {
                // 配置应用程序
                ApplicationConfiguration.Initialize();

                // 显示启动消息
                MessageBox.Show(
                    "智能优化配料系统正在启动...\n\n" +
                    "这是一个测试版本，用于验证Windows Forms功能。",
                    "系统启动",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );

                // 创建并运行测试窗体
                using var testForm = new TestForm();
                Application.Run(testForm);

                // 显示退出消息
                MessageBox.Show(
                    "感谢使用智能优化配料系统！",
                    "系统退出",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"应用程序启动失败：\n\n{ex.Message}\n\n详细信息：\n{ex.StackTrace}",
                    "启动错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }
    }
}
