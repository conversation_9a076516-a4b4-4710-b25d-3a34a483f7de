# 智能优化配料系统

## 项目简介

智能优化配料系统是一个专为钢铁企业烧结工艺设计的企业级配料优化解决方案。系统集成了先进的数学优化算法、机器学习技术和实时数据处理能力，为烧结配料过程提供智能化、自动化的优化方案。

## 主要功能

### 1. 原料及成品矿成分管理
- 原料基础信息管理（编码、名称、类型、供应商等）
- 化学成分管理（TFe、CaO、SiO2、MgO、Al2O3等）
- 物理性能管理（粒度、水分、烧损等）
- 性价比分析和采购建议
- 库存管理和预警

### 2. 配料计算与优化
- 多目标优化配料计算
- 支持人工模式和自动模式
- 集成Python优化算法（SQP、遗传算法、粒子群等）
- 实时配比调整
- 下料量智能分配
- 约束条件灵活配置

### 3. 料流跟踪与动态定位
- 全流程料流跟踪
- 灌仓管理和批次跟踪
- 下料过程实时监控
- 动态定位和预测
- 成分匹配和偏差分析
- 异常检测和处理

### 4. 烧结矿成分预测
- 基于机器学习的成分预测
- 多种预测算法支持
- 质量指标预测
- 成本预测分析
- 趋势分析和预警

### 5. 返矿配比调整
- 返矿成分分析
- 智能配比调整建议
- 质量影响评估
- 成本效益分析

## 技术架构

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   表示层 (UI)    │    │   业务逻辑层     │    │   数据访问层     │
│                 │    │                 │    │                 │
│ - WinForms界面  │◄──►│ - 配料计算服务   │◄──►│ - 数据库访问     │
│ - 图表展示      │    │ - 原料管理服务   │    │ - 文件存储       │
│ - 报表生成      │    │ - 料流跟踪服务   │    │ - 缓存管理       │
│                 │    │ - 预测分析服务   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Python算法层   │
                       │                 │
                       │ - SQP优化算法   │
                       │ - 遗传算法      │
                       │ - 神经网络      │
                       │ - 数据分析      │
                       └─────────────────┘
```

### 技术栈
- **前端**: Windows Forms (.NET 8.0)
- **后端**: C# (.NET 8.0)
- **算法**: Python (NumPy, SciPy, Pandas)
- **数据库**: SQL Server
- **缓存**: Redis (可选)
- **日志**: Microsoft.Extensions.Logging
- **配置**: Microsoft.Extensions.Configuration

## 项目结构

```
SinterOptimizationSystem/
├── src/
│   ├── SinterOptimizationSystem/              # 主应用程序
│   │   ├── Forms/                             # 窗体文件
│   │   ├── Program.cs                         # 程序入口
│   │   └── appsettings.json                   # 配置文件
│   ├── SinterOptimizationSystem.Core/         # 核心业务模型
│   │   ├── Models/                            # 数据模型
│   │   └── Interfaces/                        # 服务接口
│   └── SinterOptimizationSystem.Services/     # 业务服务实现
│       ├── MaterialManagementService.cs       # 原料管理服务
│       ├── BlendingCalculationService.cs      # 配料计算服务
│       ├── PythonAlgorithmService.cs          # Python算法集成
│       └── MaterialFlowService.cs             # 料流跟踪服务
├── py/                                        # Python算法脚本
│   └── sqp_optimization_algorithm_model.py    # SQP优化算法
├── docs/                                      # 文档
├── tests/                                     # 测试项目
└── README.md                                  # 项目说明
```

## 快速开始

### 环境要求
- .NET 8.0 SDK
- Visual Studio 2022 或 VS Code
- Python 3.8+ (用于算法计算)
- SQL Server 2019+ (可选)

### Python依赖包
```bash
pip install numpy scipy pandas matplotlib flask flask-cors
```

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd SinterOptimizationSystem
```

2. **还原NuGet包**
```bash
dotnet restore
```

3. **配置数据库连接**
编辑 `src/SinterOptimizationSystem/appsettings.json` 文件中的连接字符串。

4. **配置Python环境**
编辑配置文件中的Python路径：
```json
{
  "Python": {
    "ExecutablePath": "python",
    "ScriptsPath": "py"
  }
}
```

5. **编译运行**
```bash
dotnet build
dotnet run --project src/SinterOptimizationSystem
```

## 配置说明

### 主要配置项

#### Python配置
```json
{
  "Python": {
    "ExecutablePath": "python",           // Python可执行文件路径
    "ScriptsPath": "py",                  // Python脚本目录
    "Timeout": 300000,                    // 超时时间(毫秒)
    "MaxRetries": 3                       // 最大重试次数
  }
}
```

#### 算法配置
```json
{
  "Algorithm": {
    "DefaultAlgorithm": "SQP",            // 默认算法
    "SQP": {
      "MaxIterations": 500,               // 最大迭代次数
      "Tolerance": 1e-7,                  // 收敛容差
      "MinWetRatioThreshold": 2.0         // 最小湿配比阈值
    }
  }
}
```

#### 配料约束
```json
{
  "BlendingCalculation": {
    "Constraints": {
      "TFe": { "Min": 53.5, "Max": 56.5, "Target": 55.0 },
      "Basicity": { "Min": 1.75, "Max": 2.05, "Target": 1.90 },
      "MgO": { "Min": 1.8, "Max": 3.0, "Target": 2.39 },
      "Al2O3": { "Min": 1.5, "Max": 2.5, "Target": 1.89 }
    }
  }
}
```

## 使用指南

### 1. 原料管理
1. 启动系统后，选择"原料管理"菜单
2. 添加原料基础信息（编码、名称、类型等）
3. 录入化学成分数据
4. 设置物理性能参数
5. 配置库存信息和安全库存

### 2. 配料计算
1. 选择"配料计算"菜单
2. 设置目标参数（TFe、碱度、MgO等）
3. 选择可用原料
4. 选择计算模式（人工/自动）
5. 执行计算并查看结果
6. 根据需要调整配比

### 3. 料流跟踪
1. 选择"料流跟踪"菜单
2. 创建新的料流批次
3. 记录灌仓信息
4. 跟踪下料过程
5. 监控异常情况
6. 生成跟踪报告

## 开发指南

### 添加新的优化算法

1. **创建Python算法脚本**
```python
# py/new_algorithm.py
def optimize(input_data):
    # 算法实现
    return result
```

2. **在服务中添加调用方法**
```csharp
public async Task<PythonAlgorithmOutput> RunNewAlgorithmAsync(PythonAlgorithmInput input)
{
    // 调用Python脚本
}
```

3. **更新配置文件**
```json
{
  "Algorithm": {
    "NewAlgorithm": {
      "Parameter1": "value1"
    }
  }
}
```

### 扩展数据模型

1. 在 `SinterOptimizationSystem.Core/Models/` 中添加新模型
2. 实现相应的服务接口
3. 更新数据库架构（如果需要）
4. 添加相应的UI界面

## 故障排除

### 常见问题

1. **Python环境问题**
   - 确保Python已正确安装
   - 检查依赖包是否完整
   - 验证脚本路径配置

2. **算法计算失败**
   - 检查输入数据格式
   - 验证约束条件设置
   - 查看算法日志输出

3. **数据库连接问题**
   - 检查连接字符串
   - 确认数据库服务状态
   - 验证用户权限

### 日志查看
系统日志保存在 `logs/` 目录下，按日期分类存储。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: 开发团队
- 邮箱: <EMAIL>
- 项目地址: [GitHub Repository](https://github.com/company/SinterOptimizationSystem)

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的原料管理功能
- 集成SQP优化算法
- 提供料流跟踪基础功能
- 支持配料计算和优化

---

**注意**: 本系统为企业级应用，建议在生产环境部署前进行充分测试。
