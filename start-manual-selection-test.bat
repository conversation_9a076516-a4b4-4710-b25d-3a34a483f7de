@echo off
chcp 65001 >nul
echo ========================================
echo 智能优化配料系统 - 人工初选功能测试
echo ========================================
echo.

echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo.
echo [2/4] 检查Python依赖包...
python -c "import numpy, scipy, flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python依赖包缺失
    echo 正在安装依赖包...
    pip install numpy scipy flask
    if %errorlevel% neq 0 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)
echo ✅ Python依赖包正常

echo.
echo [3/4] 启动Python优化服务...
cd /d "%~dp0py"
start "Python优化服务" cmd /k "python optimization_service.py"
echo ✅ Python服务已启动

echo.
echo [4/4] 等待服务启动完成...
timeout /t 3 /nobreak >nul

echo.
echo [5/5] 启动C#客户端...
cd /d "%~dp0"

REM 检查是否已编译
if not exist "src\SinterOptimizationSystem\bin\Debug\net8.0-windows\SinterOptimizationSystem.exe" (
    echo 正在编译项目...
    dotnet build src\SinterOptimizationSystem\SinterOptimizationSystem.csproj -c Debug
    if %errorlevel% neq 0 (
        echo ❌ 项目编译失败
        pause
        exit /b 1
    )
)

echo ✅ 启动客户端应用程序...
start "智能优化配料系统" "src\SinterOptimizationSystem\bin\Debug\net8.0-windows\SinterOptimizationSystem.exe"

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo.
echo 使用说明：
echo 1. 主界面打开后，点击菜单 "配料计算" → "人工初选配料"
echo 2. 在物料选择页面选择参与计算的原料
echo 3. 在约束条件页面设置优化参数
echo 4. 点击"开始优化"执行计算
echo 5. 在优化结果页面查看计算结果
echo.
echo 注意事项：
echo - 确保Python服务正常运行（端口5000）
echo - 至少选择一种原料才能进行计算
echo - 计算过程可能需要几秒到几分钟时间
echo.
echo 按任意键关闭此窗口...
pause >nul
