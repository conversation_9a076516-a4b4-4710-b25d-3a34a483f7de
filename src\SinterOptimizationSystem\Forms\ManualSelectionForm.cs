using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem
{
    /// <summary>
    /// 人工初选配料优化窗体
    /// </summary>
    public partial class ManualSelectionForm : Form
    {
        private readonly ILogger<ManualSelectionForm> _logger;
        private readonly IBlendingCalculationService _blendingService;
        private readonly IMaterialManagementService _materialService;
        private readonly IPythonAlgorithmService _pythonService;

        // 控件引用
        private DataGridView _materialSelectionGrid;
        private DataGridView _constraintGrid;
        private DataGridView _resultGrid;
        private TextBox _resultTextBox;
        private Panel _progressPanel;
        private ProgressBar _progressBar;
        private Label _statusLabel;

        // 数据存储
        private List<Material> _allMaterials = new();
        private Dictionary<int, bool> _materialSelection = new();
        private Dictionary<string, (decimal Min, decimal Max)> _constraints = new();

        public ManualSelectionForm(
            ILogger<ManualSelectionForm> logger,
            IBlendingCalculationService blendingService,
            IMaterialManagementService materialService,
            IPythonAlgorithmService pythonService)
        {
            _logger = logger;
            _blendingService = blendingService;
            _materialService = materialService;
            _pythonService = pythonService;
            
            InitializeComponent();
            InitializeDataAsync();
        }

        private void InitializeComponent()
        {
            Text = "配料优化 - 人工初选";
            Size = new Size(1800, 1200);
            StartPosition = FormStartPosition.CenterParent;
            WindowState = FormWindowState.Maximized;

            // 创建主布局
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // 设置列宽和行高
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F)); // 左侧
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F)); // 右侧
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));      // 标题
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 80F));       // 主要内容
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));     // 按钮区

            // 创建标题面板
            var titlePanel = CreateTitlePanel();
            mainLayout.SetColumnSpan(titlePanel, 2);
            mainLayout.Controls.Add(titlePanel, 0, 0);

            // 创建左侧主要内容面板
            var leftPanel = CreateLeftPanel();
            mainLayout.Controls.Add(leftPanel, 0, 1);

            // 创建右侧监控面板
            var rightPanel = CreateRightPanel();
            mainLayout.Controls.Add(rightPanel, 1, 1);

            // 创建底部按钮面板
            var buttonPanel = CreateButtonPanel();
            mainLayout.SetColumnSpan(buttonPanel, 2);
            mainLayout.Controls.Add(buttonPanel, 0, 2);

            Controls.Add(mainLayout);
        }

        private Panel CreateTitlePanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(70, 130, 180)
            };

            var titleLabel = new Label
            {
                Text = "智能烧结配料优化系统 - 人工初选模式",
                Font = new Font("Microsoft YaHei", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            _statusLabel = new Label
            {
                Text = "系统状态: 就绪 | 已选择物料: 0种 | 优化状态: 待开始",
                Font = new Font("Microsoft YaHei", 10F),
                ForeColor = Color.LightGray,
                Location = new Point(10, 50),
                Size = new Size(800, 20)
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_statusLabel);
            return panel;
        }

        private Panel CreateLeftPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F)
            };

            // 物料选择标签页
            var materialTab = new TabPage("物料选择");
            materialTab.Controls.Add(CreateMaterialSelectionPanel());
            tabControl.TabPages.Add(materialTab);

            // 约束条件标签页
            var constraintTab = new TabPage("约束条件");
            constraintTab.Controls.Add(CreateConstraintPanel());
            tabControl.TabPages.Add(constraintTab);

            // 优化结果标签页
            var resultTab = new TabPage("优化结果");
            resultTab.Controls.Add(CreateResultPanel());
            tabControl.TabPages.Add(resultTab);

            panel.Controls.Add(tabControl);
            return panel;
        }

        private Panel CreateRightPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(5)
            };

            // 设置行高
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));

            // 实时监控面板
            layout.Controls.Add(CreateMonitorPanel(), 0, 0);
            layout.Controls.Add(CreateQualityPanel(), 0, 1);
            layout.Controls.Add(CreateCostPanel(), 0, 2);
            layout.Controls.Add(CreateAlarmPanel(), 0, 3);

            panel.Controls.Add(layout);
            return panel;
        }

        private GroupBox CreateMaterialSelectionPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "原料选择与成分信息",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            _materialSelectionGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                Font = new Font("Microsoft YaHei", 9F)
            };

            // 添加列
            _materialSelectionGrid.Columns.Add(CreateCheckBoxColumn("Selected", "选用"));
            _materialSelectionGrid.Columns.Add("MaterialName", "原料名称");
            _materialSelectionGrid.Columns.Add("TFe", "TFe(%)");
            _materialSelectionGrid.Columns.Add("CaO", "CaO(%)");
            _materialSelectionGrid.Columns.Add("SiO2", "SiO2(%)");
            _materialSelectionGrid.Columns.Add("MgO", "MgO(%)");
            _materialSelectionGrid.Columns.Add("Al2O3", "Al2O3(%)");
            _materialSelectionGrid.Columns.Add("H2O", "H2O(%)");
            _materialSelectionGrid.Columns.Add("Ig", "烧损(%)");
            _materialSelectionGrid.Columns.Add("Price", "价格(元/吨)");
            _materialSelectionGrid.Columns.Add("Stock", "库存(吨)");

            // 设置列宽
            _materialSelectionGrid.Columns["Selected"].Width = 60;
            _materialSelectionGrid.Columns["MaterialName"].Width = 120;

            // 绑定事件
            _materialSelectionGrid.CellValueChanged += MaterialSelectionGrid_CellValueChanged;
            _materialSelectionGrid.CurrentCellDirtyStateChanged += MaterialSelectionGrid_CurrentCellDirtyStateChanged;

            groupBox.Controls.Add(_materialSelectionGrid);
            return groupBox;
        }

        private DataGridViewCheckBoxColumn CreateCheckBoxColumn(string name, string headerText)
        {
            return new DataGridViewCheckBoxColumn
            {
                Name = name,
                HeaderText = headerText,
                TrueValue = true,
                FalseValue = false,
                Width = 60
            };
        }

        private GroupBox CreateConstraintPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "约束条件设置",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };

            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 60F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 40F));

            // 约束条件表格
            _constraintGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            _constraintGrid.Columns.Add("Parameter", "参数名称");
            _constraintGrid.Columns.Add("Target", "目标值");
            _constraintGrid.Columns.Add("MinValue", "最小值");
            _constraintGrid.Columns.Add("MaxValue", "最大值");
            _constraintGrid.Columns.Add("Weight", "权重");
            _constraintGrid.Columns.Add("Unit", "单位");

            layout.Controls.Add(_constraintGrid, 0, 0);

            // 快速设置面板
            var quickPanel = CreateQuickSettingsPanel();
            layout.Controls.Add(quickPanel, 0, 1);

            groupBox.Controls.Add(layout);
            return groupBox;
        }

        private Panel CreateQuickSettingsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(245, 245, 245)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // 添加快速设置控件
            layout.Controls.Add(new Label { Text = "快速设置:", Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold) }, 0, 0);
            
            var presetCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei", 9F)
            };
            presetCombo.Items.AddRange(new[] { "标准配置", "高品质配置", "经济配置", "自定义" });
            presetCombo.SelectedIndex = 0;
            layout.Controls.Add(presetCombo, 1, 0);

            var applyPresetBtn = new Button
            {
                Text = "应用预设",
                Font = new Font("Microsoft YaHei", 9F),
                BackColor = Color.LightBlue
            };
            applyPresetBtn.Click += ApplyPresetBtn_Click;
            layout.Controls.Add(applyPresetBtn, 2, 0);

            var resetBtn = new Button
            {
                Text = "重置",
                Font = new Font("Microsoft YaHei", 9F),
                BackColor = Color.LightCoral
            };
            resetBtn.Click += ResetBtn_Click;
            layout.Controls.Add(resetBtn, 3, 0);

            panel.Controls.Add(layout);
            return panel;
        }

        private GroupBox CreateResultPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "优化结果",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };

            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 60F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 40F));

            // 结果表格
            _resultGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            _resultGrid.Columns.Add("MaterialName", "原料名称");
            _resultGrid.Columns.Add("OptimalRatio", "优化配比(%)");
            _resultGrid.Columns.Add("FeedRate", "下料量(t/h)");
            _resultGrid.Columns.Add("Cost", "成本(元/吨)");
            _resultGrid.Columns.Add("Deviation", "偏差");

            layout.Controls.Add(_resultGrid, 0, 0);

            // 详细结果文本
            _resultTextBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                Text = "等待优化计算...\n\n操作说明：\n1. 选择参与计算的原料\n2. 设置约束条件\n3. 点击开始优化\n4. 查看计算结果"
            };

            layout.Controls.Add(_resultTextBox, 0, 1);

            groupBox.Controls.Add(layout);
            return groupBox;
        }

        private GroupBox CreateMonitorPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "系统监控",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold)
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 8F),
                Text = "系统运行正常\nAPI服务: 连接中...\n数据库: 连接正常\n最后更新: " + DateTime.Now.ToString("HH:mm:ss")
            };

            groupBox.Controls.Add(textBox);
            return groupBox;
        }

        private GroupBox CreateQualityPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "质量指标",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold)
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 8F),
                Text = "TFe: -- %\n碱度: --\nMgO: -- %\nAl2O3: -- %\n转鼓指数: -- %"
            };

            groupBox.Controls.Add(textBox);
            return groupBox;
        }

        private GroupBox CreateCostPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "成本分析",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold)
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 8F),
                Text = "预计成本: -- 元/吨\n成本节约: -- 元/吨\n性价比: --\n预算使用率: -- %"
            };

            groupBox.Controls.Add(textBox);
            return groupBox;
        }

        private GroupBox CreateAlarmPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "报警信息",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold)
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 8F),
                Text = "无报警信息",
                BackColor = Color.LightGreen
            };

            groupBox.Controls.Add(textBox);
            return groupBox;
        }

        private Panel CreateButtonPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            var layout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(10),
                WrapContents = false
            };

            // 物料选择按钮组
            var materialGroup = new GroupBox
            {
                Text = "物料选择",
                Size = new Size(200, 70),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var selectAllBtn = new Button
            {
                Text = "全选",
                Size = new Size(60, 30),
                Location = new Point(10, 25),
                Font = new Font("Microsoft YaHei", 8F)
            };
            selectAllBtn.Click += SelectAllBtn_Click;

            var selectNoneBtn = new Button
            {
                Text = "全不选",
                Size = new Size(60, 30),
                Location = new Point(80, 25),
                Font = new Font("Microsoft YaHei", 8F)
            };
            selectNoneBtn.Click += SelectNoneBtn_Click;

            var recommendBtn = new Button
            {
                Text = "推荐选择",
                Size = new Size(80, 30),
                Location = new Point(150, 25),
                Font = new Font("Microsoft YaHei", 8F),
                BackColor = Color.LightGreen
            };
            recommendBtn.Click += RecommendBtn_Click;

            materialGroup.Controls.AddRange(new Control[] { selectAllBtn, selectNoneBtn, recommendBtn });
            layout.Controls.Add(materialGroup);

            // 优化计算按钮组
            var optimizeGroup = new GroupBox
            {
                Text = "优化计算",
                Size = new Size(250, 70),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var startBtn = new Button
            {
                Text = "开始优化",
                Size = new Size(70, 30),
                Location = new Point(10, 25),
                Font = new Font("Microsoft YaHei", 8F, FontStyle.Bold),
                BackColor = Color.LightGreen
            };
            startBtn.Click += StartOptimizeBtn_Click;

            var stopBtn = new Button
            {
                Text = "停止",
                Size = new Size(50, 30),
                Location = new Point(90, 25),
                Font = new Font("Microsoft YaHei", 8F),
                BackColor = Color.LightCoral
            };
            stopBtn.Click += StopBtn_Click;

            var validateBtn = new Button
            {
                Text = "验证方案",
                Size = new Size(70, 30),
                Location = new Point(150, 25),
                Font = new Font("Microsoft YaHei", 8F),
                BackColor = Color.LightBlue
            };
            validateBtn.Click += ValidateBtn_Click;

            optimizeGroup.Controls.AddRange(new Control[] { startBtn, stopBtn, validateBtn });
            layout.Controls.Add(optimizeGroup);

            // 数据管理按钮组
            var dataGroup = new GroupBox
            {
                Text = "数据管理",
                Size = new Size(200, 70),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var saveBtn = new Button
            {
                Text = "保存方案",
                Size = new Size(70, 30),
                Location = new Point(10, 25),
                Font = new Font("Microsoft YaHei", 8F)
            };
            saveBtn.Click += SaveBtn_Click;

            var loadBtn = new Button
            {
                Text = "加载方案",
                Size = new Size(70, 30),
                Location = new Point(90, 25),
                Font = new Font("Microsoft YaHei", 8F)
            };
            loadBtn.Click += LoadBtn_Click;

            var exportBtn = new Button
            {
                Text = "导出",
                Size = new Size(50, 30),
                Location = new Point(170, 25),
                Font = new Font("Microsoft YaHei", 8F)
            };
            exportBtn.Click += ExportBtn_Click;

            dataGroup.Controls.AddRange(new Control[] { saveBtn, loadBtn, exportBtn });
            layout.Controls.Add(dataGroup);

            // 进度显示
            _progressPanel = new Panel
            {
                Size = new Size(300, 70),
                BorderStyle = BorderStyle.FixedSingle
            };

            _progressBar = new ProgressBar
            {
                Location = new Point(10, 20),
                Size = new Size(280, 20),
                Style = ProgressBarStyle.Continuous
            };

            var progressLabel = new Label
            {
                Text = "计算进度",
                Location = new Point(10, 45),
                Size = new Size(280, 20),
                Font = new Font("Microsoft YaHei", 8F),
                TextAlign = ContentAlignment.MiddleCenter
            };

            _progressPanel.Controls.AddRange(new Control[] { _progressBar, progressLabel });
            layout.Controls.Add(_progressPanel);

            panel.Controls.Add(layout);
            return panel;
        }

        #region 数据初始化

        private async void InitializeDataAsync()
        {
            try
            {
                _statusLabel.Text = "系统状态: 正在加载数据... | 已选择物料: 0种 | 优化状态: 待开始";

                // 加载物料数据
                _allMaterials = (await _materialService.GetAllMaterialsAsync()).ToList();

                // 初始化物料选择状态
                foreach (var material in _allMaterials)
                {
                    _materialSelection[material.Id] = false;
                }

                // 填充物料选择表格
                PopulateMaterialSelectionGrid();

                // 初始化约束条件
                InitializeConstraints();

                _statusLabel.Text = $"系统状态: 数据加载完成 | 可选物料: {_allMaterials.Count}种 | 已选择物料: 0种 | 优化状态: 待开始";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化数据失败");
                MessageBox.Show($"初始化数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateMaterialSelectionGrid()
        {
            _materialSelectionGrid.Rows.Clear();

            foreach (var material in _allMaterials)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_materialSelectionGrid);

                row.Cells["Selected"].Value = false;
                row.Cells["MaterialName"].Value = material.Name;

                // 获取化学成分数据（这里使用模拟数据，实际应从数据库获取）
                var compositions = GetMaterialCompositions(material.Id);
                row.Cells["TFe"].Value = compositions.GetValueOrDefault("TFe", 0).ToString("F2");
                row.Cells["CaO"].Value = compositions.GetValueOrDefault("CaO", 0).ToString("F2");
                row.Cells["SiO2"].Value = compositions.GetValueOrDefault("SiO2", 0).ToString("F2");
                row.Cells["MgO"].Value = compositions.GetValueOrDefault("MgO", 0).ToString("F2");
                row.Cells["Al2O3"].Value = compositions.GetValueOrDefault("Al2O3", 0).ToString("F2");
                row.Cells["H2O"].Value = compositions.GetValueOrDefault("H2O", 0).ToString("F2");
                row.Cells["Ig"].Value = compositions.GetValueOrDefault("Ig", 0).ToString("F2");
                row.Cells["Price"].Value = material.Price.ToString("F2");
                row.Cells["Stock"].Value = material.CurrentStock.ToString("F1");

                row.Tag = material.Id;
                _materialSelectionGrid.Rows.Add(row);
            }
        }

        private Dictionary<string, decimal> GetMaterialCompositions(int materialId)
        {
            // 模拟数据，实际应从数据库获取
            var compositions = new Dictionary<string, decimal>();

            switch (materialId % 14) // 模拟14种不同的物料
            {
                case 0: // 碱性精粉
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 63.76m, ["CaO"] = 1.94m, ["SiO2"] = 4.95m, ["MgO"] = 1.85m,
                        ["Al2O3"] = 0.60m, ["H2O"] = 8.20m, ["Ig"] = 1.23m
                    };
                    break;
                case 1: // 酸性精粉
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 64.89m, ["CaO"] = 0.70m, ["SiO2"] = 6.32m, ["MgO"] = 0.92m,
                        ["Al2O3"] = 0.72m, ["H2O"] = 9.90m, ["Ig"] = -0.05m
                    };
                    break;
                case 2: // 海瑞
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 58.07m, ["CaO"] = 0.10m, ["SiO2"] = 6.21m, ["MgO"] = 0.28m,
                        ["Al2O3"] = 2.52m, ["H2O"] = 6.00m, ["Ig"] = 9.07m
                    };
                    break;
                case 3: // 印粉海娜
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 63.66m, ["CaO"] = 0.10m, ["SiO2"] = 4.01m, ["MgO"] = 0.24m,
                        ["Al2O3"] = 2.42m, ["H2O"] = 6.70m, ["Ig"] = 1.60m
                    };
                    break;
                case 4: // 巴西粗粉
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 64.64m, ["CaO"] = 0.20m, ["SiO2"] = 4.69m, ["MgO"] = 0.11m,
                        ["Al2O3"] = 0.73m, ["H2O"] = 6.70m, ["Ig"] = 1.33m
                    };
                    break;
                case 5: // 俄罗斯精粉
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 62.95m, ["CaO"] = 1.71m, ["SiO2"] = 4.61m, ["MgO"] = 3.70m,
                        ["Al2O3"] = 2.29m, ["H2O"] = 10.00m, ["Ig"] = -0.35m
                    };
                    break;
                case 6: // 高炉返矿
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 55.54m, ["CaO"] = 10.60m, ["SiO2"] = 5.59m, ["MgO"] = 2.34m,
                        ["Al2O3"] = 2.09m, ["H2O"] = 0.50m, ["Ig"] = 1.73m
                    };
                    break;
                case 7: // 回收料
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 56.16m, ["CaO"] = 6.56m, ["SiO2"] = 6.31m, ["MgO"] = 2.39m,
                        ["Al2O3"] = 2.51m, ["H2O"] = 10.73m, ["Ig"] = 1.74m
                    };
                    break;
                case 8: // 钢渣
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 26.46m, ["CaO"] = 28.15m, ["SiO2"] = 15.43m, ["MgO"] = 2.79m,
                        ["Al2O3"] = 2.53m, ["H2O"] = 7.60m, ["Ig"] = 12.05m
                    };
                    break;
                case 9: // 氧化铁皮
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 69.73m, ["CaO"] = 0.50m, ["SiO2"] = 1.50m, ["MgO"] = 0.00m,
                        ["Al2O3"] = 2.88m, ["H2O"] = 5.90m, ["Ig"] = -1.52m
                    };
                    break;
                case 10: // 生石灰
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 0.00m, ["CaO"] = 71.74m, ["SiO2"] = 3.52m, ["MgO"] = 2.28m,
                        ["Al2O3"] = 1.19m, ["H2O"] = 7.00m, ["Ig"] = 16.33m
                    };
                    break;
                case 11: // 轻烧白云石
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 0.00m, ["CaO"] = 42.67m, ["SiO2"] = 5.31m, ["MgO"] = 26.12m,
                        ["Al2O3"] = 0.10m, ["H2O"] = 1.50m, ["Ig"] = 19.73m
                    };
                    break;
                case 12: // 焦粉
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 0.19m, ["CaO"] = 0.37m, ["SiO2"] = 8.82m, ["MgO"] = 0.22m,
                        ["Al2O3"] = 3.31m, ["H2O"] = 13.15m, ["Ig"] = 79.40m
                    };
                    break;
                case 13: // 澳粉纵横
                    compositions = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 60.80m, ["CaO"] = 0.10m, ["SiO2"] = 4.35m, ["MgO"] = 0.20m,
                        ["Al2O3"] = 2.30m, ["H2O"] = 8.30m, ["Ig"] = 6.89m
                    };
                    break;
            }

            return compositions;
        }

        private void InitializeConstraints()
        {
            _constraintGrid.Rows.Clear();

            var constraints = new[]
            {
                new { Parameter = "TFe含量", Target = "55.0", Min = "53.5", Max = "56.5", Weight = "0.5", Unit = "%" },
                new { Parameter = "碱度(R)", Target = "1.90", Min = "1.75", Max = "2.05", Weight = "0.3", Unit = "-" },
                new { Parameter = "MgO含量", Target = "2.39", Min = "1.8", Max = "3.0", Weight = "0.1", Unit = "%" },
                new { Parameter = "Al2O3含量", Target = "1.89", Min = "1.5", Max = "2.5", Weight = "0.1", Unit = "%" },
                new { Parameter = "成本", Target = "632.5", Min = "600", Max = "665", Weight = "0.0", Unit = "元/吨" }
            };

            foreach (var constraint in constraints)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_constraintGrid);
                row.Cells["Parameter"].Value = constraint.Parameter;
                row.Cells["Target"].Value = constraint.Target;
                row.Cells["MinValue"].Value = constraint.Min;
                row.Cells["MaxValue"].Value = constraint.Max;
                row.Cells["Weight"].Value = constraint.Weight;
                row.Cells["Unit"].Value = constraint.Unit;
                _constraintGrid.Rows.Add(row);
            }

            // 设置约束条件字典
            _constraints["TFe"] = (53.5m, 56.5m);
            _constraints["R"] = (1.75m, 2.05m);
            _constraints["MgO"] = (1.8m, 3.0m);
            _constraints["Al2O3"] = (1.5m, 2.5m);
            _constraints["Cost"] = (600m, 665m);
        }

        #endregion

        #region 事件处理

        private void MaterialSelectionGrid_CellValueChanged(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var grid = sender as DataGridView;
                if (grid?.Columns[e.ColumnIndex].Name == "Selected")
                {
                    var materialId = (int)grid.Rows[e.RowIndex].Tag;
                    var isSelected = (bool)grid.Rows[e.RowIndex].Cells["Selected"].Value;
                    _materialSelection[materialId] = isSelected;

                    UpdateStatusLabel();
                }
            }
        }

        private void MaterialSelectionGrid_CurrentCellDirtyStateChanged(object? sender, EventArgs e)
        {
            var grid = sender as DataGridView;
            if (grid?.IsCurrentCellDirty == true)
            {
                grid.CommitEdit(DataGridViewDataErrorContexts.Commit);
            }
        }

        private void UpdateStatusLabel()
        {
            var selectedCount = _materialSelection.Values.Count(x => x);
            _statusLabel.Text = $"系统状态: 就绪 | 可选物料: {_allMaterials.Count}种 | 已选择物料: {selectedCount}种 | 优化状态: 待开始";
        }

        private void SelectAllBtn_Click(object? sender, EventArgs e)
        {
            foreach (DataGridViewRow row in _materialSelectionGrid.Rows)
            {
                row.Cells["Selected"].Value = true;
                var materialId = (int)row.Tag;
                _materialSelection[materialId] = true;
            }
            UpdateStatusLabel();
        }

        private void SelectNoneBtn_Click(object? sender, EventArgs e)
        {
            foreach (DataGridViewRow row in _materialSelectionGrid.Rows)
            {
                row.Cells["Selected"].Value = false;
                var materialId = (int)row.Tag;
                _materialSelection[materialId] = false;
            }
            UpdateStatusLabel();
        }

        private void RecommendBtn_Click(object? sender, EventArgs e)
        {
            // 推荐选择：选择性价比高、库存充足的物料
            var recommendedMaterials = new[] { "印粉海娜", "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "生石灰", "轻烧白云石", "焦粉", "澳粉纵横" };

            foreach (DataGridViewRow row in _materialSelectionGrid.Rows)
            {
                var materialName = row.Cells["MaterialName"].Value?.ToString();
                var isRecommended = recommendedMaterials.Contains(materialName);
                row.Cells["Selected"].Value = isRecommended;

                var materialId = (int)row.Tag;
                _materialSelection[materialId] = isRecommended;
            }
            UpdateStatusLabel();
        }

        private void ApplyPresetBtn_Click(object? sender, EventArgs e)
        {
            // 应用预设配置
            MessageBox.Show("预设配置功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ResetBtn_Click(object? sender, EventArgs e)
        {
            // 重置所有设置
            SelectNoneBtn_Click(sender, e);
            InitializeConstraints();
            _resultGrid.Rows.Clear();
            _resultTextBox.Text = "等待优化计算...\n\n操作说明：\n1. 选择参与计算的原料\n2. 设置约束条件\n3. 点击开始优化\n4. 查看计算结果";
        }

        private async void StartOptimizeBtn_Click(object? sender, EventArgs e)
        {
            try
            {
                var selectedMaterials = _materialSelection.Where(x => x.Value).Select(x => x.Key).ToList();
                if (selectedMaterials.Count == 0)
                {
                    MessageBox.Show("请至少选择一种原料参与计算", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _statusLabel.Text = $"系统状态: 计算中... | 已选择物料: {selectedMaterials.Count}种 | 优化状态: 正在计算";
                _progressBar.Value = 0;
                _progressBar.Style = ProgressBarStyle.Marquee;

                _resultTextBox.Text = "正在执行配料优化计算...\n";

                // 创建计算请求
                var request = new BlendingCalculationRequest
                {
                    Mode = BlendingMode.Automatic,
                    TargetTFe = 55.0m,
                    TargetBasicity = 1.90m,
                    TargetMgO = 2.39m,
                    TargetAl2O3 = 1.89m,
                    AvailableMaterialIds = selectedMaterials,
                    CostLimit = 665m
                };

                var result = await _blendingService.CalculateBlendingAsync(request);

                _progressBar.Style = ProgressBarStyle.Continuous;
                _progressBar.Value = 100;

                if (result.IsSuccess)
                {
                    DisplayOptimizationResult(result);
                    _statusLabel.Text = $"系统状态: 计算完成 | 已选择物料: {selectedMaterials.Count}种 | 优化状态: 成功";
                }
                else
                {
                    _resultTextBox.Text = $"❌ 优化计算失败!\n\n错误信息: {result.ErrorMessage}\n\n建议检查:\n• 选择的物料是否合理\n• 约束条件是否过于严格\n• API服务是否正常";
                    _statusLabel.Text = $"系统状态: 计算失败 | 已选择物料: {selectedMaterials.Count}种 | 优化状态: 失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "优化计算异常");
                MessageBox.Show($"优化计算异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _statusLabel.Text = "系统状态: 异常 | 优化状态: 异常";
            }
        }

        private void DisplayOptimizationResult(BlendingCalculationResult result)
        {
            // 清空结果表格
            _resultGrid.Rows.Clear();

            // 填充结果数据
            foreach (var detail in result.BlendingDetails)
            {
                var material = _allMaterials.FirstOrDefault(m => m.Id == detail.MaterialId);
                if (material != null && detail.WetRatio > 0.01m)
                {
                    var row = new DataGridViewRow();
                    row.CreateCells(_resultGrid);
                    row.Cells["MaterialName"].Value = material.Name;
                    row.Cells["OptimalRatio"].Value = detail.WetRatio.ToString("F2");
                    row.Cells["FeedRate"].Value = (detail.WetRatio * 100 / 100).ToString("F2"); // 假设总产量100t/h
                    row.Cells["Cost"].Value = material.Price.ToString("F2");
                    row.Cells["Deviation"].Value = "0.00"; // 计算偏差
                    _resultGrid.Rows.Add(row);
                }
            }

            // 更新详细结果文本
            _resultTextBox.Text = $"✅ 优化计算完成!\n\n" +
                                $"📊 计算统计:\n" +
                                $"  • 计算时间: {result.CalculationTime}ms\n" +
                                $"  • 迭代次数: {result.IterationCount}\n" +
                                $"  • 目标函数值: {result.ObjectiveValue:F6}\n" +
                                $"  • 约束满足: {(result.ConstraintsSatisfied?.Values.All(v => v) == true ? "是" : "否")}\n\n" +
                                $"🎯 最优配比方案:\n" +
                                string.Join("\n", result.BlendingDetails.Where(d => d.WetRatio > 0.01m)
                                    .Select(d => $"  • {_allMaterials.FirstOrDefault(m => m.Id == d.MaterialId)?.Name}: {d.WetRatio:F2}%")) +
                                $"\n\n⏰ 完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
        }

        private void StopBtn_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("停止计算功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ValidateBtn_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("验证方案功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SaveBtn_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("保存方案功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadBtn_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("加载方案功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExportBtn_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("导出功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }
}
