using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using System.Text;
using System.Collections.Generic;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Services
{
    /// <summary>
    /// Python算法集成服务实现
    /// </summary>
    public class PythonAlgorithmService : IPythonAlgorithmService
    {
        private readonly ILogger<PythonAlgorithmService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;
        private readonly Dictionary<string, PythonAlgorithmOutput> _cache;

        public PythonAlgorithmService(ILogger<PythonAlgorithmService> logger, IConfiguration configuration, HttpClient httpClient)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
            _apiBaseUrl = _configuration["Python:ApiBaseUrl"] ?? "http://localhost:5000";
            _cache = new Dictionary<string, PythonAlgorithmOutput>();

            // 配置HttpClient
            var timeoutValue = _configuration["Python:Timeout"];
            var timeout = int.TryParse(timeoutValue, out var t) ? t : 300;
            _httpClient.Timeout = TimeSpan.FromSeconds(timeout);
        }

        #region 算法调用

        public async Task<PythonAlgorithmOutput> RunSQPOptimizationAsync(PythonAlgorithmInput input)
        {
            try
            {
                _logger.LogInformation("开始执行SQP优化算法");

                // 检查缓存
                var inputHash = CalculateInputHash(input);
                var cachedResult = await GetCachedCalculationResultAsync(inputHash);
                if (cachedResult != null)
                {
                    _logger.LogInformation("使用缓存的计算结果");
                    return cachedResult;
                }

                var stopwatch = Stopwatch.StartNew();

                // 转换为HTTP API请求格式
                var apiRequest = ConvertToApiRequest(input);
                var requestJson = JsonConvert.SerializeObject(apiRequest);
                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

                // 调用HTTP API
                var response = await _httpClient.PostAsync($"{_apiBaseUrl}/api/optimize", content);

                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("API响应内容: {ResponseJson}", responseJson);
                    
                    ApiOptimizeResponse? apiResponse;
                    try
                    {
                        apiResponse = JsonConvert.DeserializeObject<ApiOptimizeResponse>(responseJson);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "API响应反序列化失败: {ResponseJson}", responseJson);
                        return new PythonAlgorithmOutput
                        {
                            IsSuccess = false,
                            ErrorMessage = $"API响应反序列化失败: {ex.Message}",
                            CalculationTime = stopwatch.ElapsedMilliseconds
                        };
                    }

                    if (apiResponse != null)
                    {
                        _logger.LogInformation("API响应反序列化成功: Success={Success}, IterationCount={IterationCount}", apiResponse.Success, apiResponse.IterationCount);

                        if (apiResponse.Success)
                        {
                            if (apiResponse.OptimalRatios == null || apiResponse.OptimalRatios.Count == 0)
                            {
                                _logger.LogWarning("API返回成功但最优配比为空");
                                return new PythonAlgorithmOutput
                                {
                                    IsSuccess = false,
                                    ErrorMessage = "API返回成功但最优配比为空",
                                    CalculationTime = stopwatch.ElapsedMilliseconds
                                };
                            }

                            var output = ConvertFromApiResponse(apiResponse);
                            output.CalculationTime = stopwatch.ElapsedMilliseconds;

                            // 缓存结果
                            await CacheCalculationResultAsync(inputHash, output);

                            _logger.LogInformation("SQP优化算法执行成功，耗时: {Time}ms", stopwatch.ElapsedMilliseconds);
                            return output;
                        }
                        else
                        {
                            _logger.LogWarning("API返回失败: {Error}", apiResponse.Error);
                            return new PythonAlgorithmOutput
                            {
                                IsSuccess = false,
                                ErrorMessage = apiResponse.Error ?? "API调用失败",
                                CalculationTime = stopwatch.ElapsedMilliseconds
                            };
                        }
                    }
                    else
                    {
                        _logger.LogError("API响应反序列化为null: {ResponseJson}", responseJson);
                        return new PythonAlgorithmOutput
                        {
                            IsSuccess = false,
                            ErrorMessage = "API响应解析失败",
                            CalculationTime = stopwatch.ElapsedMilliseconds
                        };
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("HTTP API调用失败: {StatusCode}, {Content}", response.StatusCode, errorContent);

                    return new PythonAlgorithmOutput
                    {
                        IsSuccess = false,
                        ErrorMessage = $"HTTP API调用失败: {response.StatusCode}",
                        CalculationTime = stopwatch.ElapsedMilliseconds
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SQP优化算法执行异常");
                return new PythonAlgorithmOutput
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region 算法管理

        public async Task<(bool IsAvailable, string Version, string[] MissingPackages)> CheckPythonEnvironmentAsync()
        {
            try
            {
                _logger.LogInformation("检查Python API服务");

                // 检查API健康状态
                var response = await _httpClient.GetAsync($"{_apiBaseUrl}/health");

                if (response.IsSuccessStatusCode)
                {
                    var healthJson = await response.Content.ReadAsStringAsync();
                    var healthResponse = JsonConvert.DeserializeObject<dynamic>(healthJson);

                    // 获取服务信息
                    var infoResponse = await _httpClient.GetAsync($"{_apiBaseUrl}/info");
                    if (infoResponse.IsSuccessStatusCode)
                    {
                        var infoJson = await infoResponse.Content.ReadAsStringAsync();
                        var info = JsonConvert.DeserializeObject<Dictionary<string, object>>(infoJson);

                        var version = info?.GetValueOrDefault("python_version")?.ToString() ?? "未知版本";

                        _logger.LogInformation("Python API服务检查完成，版本: {Version}", version);
                        return (true, version, Array.Empty<string>());
                    }
                    else
                    {
                        _logger.LogWarning("无法获取Python API服务信息");
                        return (true, "未知版本", Array.Empty<string>());
                    }
                }
                else
                {
                    _logger.LogError("Python API服务不可用: {StatusCode}", response.StatusCode);
                    return (false, "", new[] { $"Python API服务不可用: {response.StatusCode}" });
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "无法连接到Python API服务");
                return (false, "", new[] { $"无法连接到Python API服务: {ex.Message}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查Python环境异常");
                return (false, "", new[] { ex.Message });
            }
        }

        public async Task<bool> InstallPythonPackagesAsync(string[] packages)
        {
            try
            {
                _logger.LogInformation("安装Python包: {Packages}", string.Join(", ", packages));

                foreach (var package in packages)
                {
                    var result = await ExecutePythonScriptAsync($"-m pip install {package}");
                    if (!result.IsSuccess)
                    {
                        _logger.LogError("安装包失败: {Package}, 错误: {Error}", package, result.ErrorMessage);
                        return false;
                    }
                }

                _logger.LogInformation("Python包安装成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安装Python包异常");
                return false;
            }
        }

        public async Task<bool> UpdateAlgorithmScriptAsync(string scriptPath, string scriptContent)
        {
            try
            {
                _logger.LogInformation("更新算法脚本功能暂不支持HTTP API模式: {ScriptPath}", scriptPath);
                return await Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新算法脚本异常: {ScriptPath}", scriptPath);
                return false;
            }
        }

        public async Task<Dictionary<string, string>> GetAlgorithmVersionsAsync()
        {
            try
            {
                // 通过API获取版本信息
                var response = await _httpClient.GetAsync($"{_apiBaseUrl}/info");
                if (response.IsSuccessStatusCode)
                {
                    var infoJson = await response.Content.ReadAsStringAsync();
                    var info = JsonConvert.DeserializeObject<dynamic>(infoJson);

                    var versions = new Dictionary<string, string>();
                    if (info?.version != null)
                    {
                        versions["optimization_service"] = info.version.ToString();
                    }
                    return versions;
                }

                return new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取算法版本信息异常");
                return new Dictionary<string, string>();
            }
        }

        #endregion

        #region 性能监控

        public async Task<Dictionary<string, object>> GetAlgorithmPerformanceStatsAsync(string algorithmType, int days = 7)
        {
            // 简化实现，实际应从数据库获取统计信息
            return await Task.FromResult(new Dictionary<string, object>
            {
                ["TotalExecutions"] = 0,
                ["AverageExecutionTime"] = 0,
                ["SuccessRate"] = 0.0,
                ["ErrorCount"] = 0
            });
        }

        public async Task<bool> LogAlgorithmExecutionAsync(string algorithmType, PythonAlgorithmInput input, PythonAlgorithmOutput output, long executionTime)
        {
            try
            {
                _logger.LogInformation("记录算法执行日志: {AlgorithmType}, 耗时: {ExecutionTime}ms, 成功: {IsSuccess}", 
                    algorithmType, executionTime, output.IsSuccess);
                
                // 实际应保存到数据库
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录算法执行日志异常");
                return false;
            }
        }

        #endregion

        #region 数据转换

        public async Task<PythonAlgorithmInput> ConvertToPythonInputAsync(
            IEnumerable<Material> materials,
            decimal targetTFe,
            decimal targetBasicity,
            decimal targetMgO,
            decimal targetAl2O3,
            List<int> availableMaterialIds,
            Dictionary<int, decimal>? fixedRatios = null,
            decimal? costLimit = null)
        {
            try
            {
                var materialList = materials.ToList();
                var materialNames = materialList.Select(m => m.Name).ToList();
                
                // 构建原料化学成分矩阵
                var materialsMatrix = new decimal[materialList.Count, 8]; // TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price
                var manualSelection = new bool[materialList.Count];
                var initialRatios = new decimal[materialList.Count];
                var boundsMatrix = new decimal[materialList.Count, 2];

                for (int i = 0; i < materialList.Count; i++)
                {
                    var material = materialList[i];
                    
                    // 设置化学成分（这里使用示例数据，实际应从数据库获取）
                    materialsMatrix[i, 7] = material.Price; // Price
                    
                    // 设置人工选择标志
                    manualSelection[i] = availableMaterialIds.Contains(material.Id);

                    // 设置初始配比
                    initialRatios[i] = fixedRatios?.GetValueOrDefault(material.Id, 0) ?? 0;
                    
                    // 设置边界条件
                    boundsMatrix[i, 0] = material.MinRatio; // 最小值
                    boundsMatrix[i, 1] = material.MaxRatio; // 最大值
                }

                var input = new PythonAlgorithmInput
                {
                    MaterialNames = materialNames,
                    MaterialsMatrix = materialsMatrix,
                    ManualSelection = manualSelection,
                    Targets = new Dictionary<string, decimal>
                    {
                        ["TFe"] = targetTFe,
                        ["R"] = targetBasicity,
                        ["MgO"] = targetMgO,
                        ["Al2O3"] = targetAl2O3
                    },
                    Ranges = new Dictionary<string, decimal[]>
                    {
                        ["TFe"] = new[] { targetTFe - 1.5m, targetTFe + 1.5m },
                        ["R"] = new[] { targetBasicity - 0.15m, targetBasicity + 0.15m },
                        ["MgO"] = new[] { targetMgO - 0.6m, targetMgO + 0.6m },
                        ["Al2O3"] = new[] { targetAl2O3 - 0.5m, targetAl2O3 + 0.5m },
                        ["Cost"] = new[] { 600m, costLimit ?? 665m }
                    },
                    Weights = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 0.5m,
                        ["R"] = 0.3m,
                        ["MgO"] = 0.1m,
                        ["Al2O3"] = 0.1m
                    },
                    InitialRatios = initialRatios,
                    BoundsMatrix = boundsMatrix
                };

                return await Task.FromResult(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换为Python输入格式异常");
                throw;
            }
        }

        public async Task<List<BlendingDetail>> ConvertFromPythonOutputAsync(PythonAlgorithmOutput pythonOutput, IEnumerable<Material> materials)
        {
            try
            {
                var blendingDetails = new List<BlendingDetail>();

                if (pythonOutput.IsSuccess && pythonOutput.OptimalRatios.Length > 0)
                {
                    var materialList = materials.ToList();

                    for (int i = 0; i < pythonOutput.OptimalRatios.Length && i < materialList.Count; i++)
                    {
                        if (pythonOutput.OptimalRatios[i] > 0.01m) // 只包含有效配比
                        {
                            blendingDetails.Add(new BlendingDetail
                            {
                                MaterialId = materialList[i].Id,
                                Material = materialList[i],
                                WetRatio = pythonOutput.OptimalRatios[i],
                                DryRatio = pythonOutput.OptimalRatios[i] * 0.9m, // 简化计算
                                CostContribution = pythonOutput.OptimalRatios[i] / 100 * materialList[i].Price,
                                IsEnabled = true
                            });
                        }
                    }
                }

                return await Task.FromResult(blendingDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换Python输出格式异常");
                throw;
            }
        }

        #endregion

        #region 缓存管理

        public async Task<bool> CacheCalculationResultAsync(string inputHash, PythonAlgorithmOutput output, int expireMinutes = 60)
        {
            try
            {
                _cache[inputHash] = output;
                
                // 简化实现，实际应使用Redis等缓存系统
                _ = Task.Delay(TimeSpan.FromMinutes(expireMinutes)).ContinueWith(_ => _cache.Remove(inputHash));
                
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缓存计算结果异常");
                return false;
            }
        }

        public async Task<PythonAlgorithmOutput?> GetCachedCalculationResultAsync(string inputHash)
        {
            try
            {
                return await Task.FromResult(_cache.GetValueOrDefault(inputHash));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存计算结果异常");
                return null;
            }
        }

        public async Task<bool> ClearExpiredCacheAsync()
        {
            try
            {
                // 简化实现
                _cache.Clear();
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期缓存异常");
                return false;
            }
        }

        #endregion

        #region API数据模型

        /// <summary>
        /// API优化请求模型
        /// </summary>
        private class ApiOptimizeRequest
        {
            [JsonProperty("targetTfe")]
            public decimal TargetTfe { get; set; }

            [JsonProperty("targetR")]
            public decimal TargetR { get; set; }

            [JsonProperty("targetMgO")]
            public decimal TargetMgO { get; set; }

            [JsonProperty("targetAl2O3")]
            public decimal TargetAl2O3 { get; set; }

            [JsonProperty("materialSelection")]
            public bool[]? MaterialSelection { get; set; }

            [JsonProperty("minRatioThreshold")]
            public decimal? MinRatioThreshold { get; set; }

            [JsonProperty("constraintRanges")]
            public Dictionary<string, decimal[]>? ConstraintRanges { get; set; }

            [JsonProperty("optimizationWeights")]
            public Dictionary<string, decimal>? OptimizationWeights { get; set; }
        }

        /// <summary>
        /// API优化响应模型
        /// </summary>
        private class ApiOptimizeResponse
        {
            [JsonProperty("success")]
            public bool Success { get; set; }

            [JsonProperty("error")]
            public string? Error { get; set; }

            [JsonProperty("optimalRatios")]
            public Dictionary<string, decimal>? OptimalRatios { get; set; }

            [JsonProperty("finalProperties")]
            public Dictionary<string, decimal>? FinalProperties { get; set; }

            [JsonProperty("iterationCount")]
            public int IterationCount { get; set; }

            [JsonProperty("objectiveValue")]
            public decimal ObjectiveValue { get; set; }

            [JsonProperty("constraintsSatisfied")]
            public Dictionary<string, bool>? ConstraintsSatisfied { get; set; }

            [JsonProperty("warnings")]
            public string[]? Warnings { get; set; }

            [JsonProperty("algorithmDetails")]
            public JObject? AlgorithmDetails { get; set; }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 转换为API请求格式
        /// </summary>
        private ApiOptimizeRequest ConvertToApiRequest(PythonAlgorithmInput input)
        {
            return new ApiOptimizeRequest
            {
                TargetTfe = input.Targets.GetValueOrDefault("TFe", 55.0m),
                TargetR = input.Targets.GetValueOrDefault("R", 1.90m),
                TargetMgO = input.Targets.GetValueOrDefault("MgO", 2.39m),
                TargetAl2O3 = input.Targets.GetValueOrDefault("Al2O3", 1.89m),
                MaterialSelection = input.ManualSelection,
                MinRatioThreshold = input.MinWetRatioThreshold,
                ConstraintRanges = input.Ranges,
                OptimizationWeights = input.Weights
            };
        }

        /// <summary>
        /// 从API响应转换为输出格式
        /// </summary>
        private PythonAlgorithmOutput ConvertFromApiResponse(ApiOptimizeResponse apiResponse)
        {
            // 定义标准物料顺序（与Python服务端保持一致）
            var standardMaterialOrder = new List<string>
            {
                "印粉海娜", "俄罗斯精粉", "高炉返矿", "回收料", "钢渣",
                "生石灰", "轻烧白云石", "焦粉", "澳粉纵横"
            };

            // 处理optimalRatios为Dictionary<string, decimal>的情况
            decimal[] optimalRatiosArray = Array.Empty<decimal>();
            if (apiResponse.OptimalRatios != null && apiResponse.OptimalRatios.Count > 0)
            {
                // 按标准物料顺序提取配比值
                optimalRatiosArray = standardMaterialOrder
                    .Select(name => apiResponse.OptimalRatios.GetValueOrDefault(name, 0m))
                    .ToArray();
            }
            
            return new PythonAlgorithmOutput
            {
                IsSuccess = apiResponse.Success,
                ErrorMessage = apiResponse.Error,
                OptimalRatios = optimalRatiosArray,
                FinalProperties = apiResponse.FinalProperties ?? new Dictionary<string, decimal>(),
                IterationCount = apiResponse.IterationCount,
                ObjectiveValue = apiResponse.ObjectiveValue,
                ConstraintsSatisfied = apiResponse.ConstraintsSatisfied ?? new Dictionary<string, bool>(),
                Warnings = apiResponse.Warnings?.ToList() ?? new List<string>(),
                DetailedResults = apiResponse.AlgorithmDetails?.ToString()
            };
        }

        /// <summary>
        /// 执行Python脚本（已弃用，保留用于兼容性）
        /// </summary>
        private async Task<(bool IsSuccess, string? Output, string? ErrorMessage)> ExecutePythonScriptAsync(string arguments)
        {
            // 在HTTP API模式下不再支持直接执行Python脚本
            _logger.LogWarning("ExecutePythonScriptAsync已弃用，请使用HTTP API模式");
            return await Task.FromResult<(bool IsSuccess, string? Output, string? ErrorMessage)>((false, null, "HTTP API模式下不支持直接执行Python脚本"));
        }

        /// <summary>
        /// 计算输入参数哈希值
        /// </summary>
        private string CalculateInputHash(PythonAlgorithmInput input)
        {
            var json = JsonConvert.SerializeObject(input);
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(json));
            return Convert.ToBase64String(hash);
        }

        #endregion
    }
}
