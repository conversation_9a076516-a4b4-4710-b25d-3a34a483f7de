using Microsoft.Extensions.Logging;
using Moq;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;
using SinterOptimizationSystem.Services;
using Xunit;

namespace SinterOptimizationSystem.Tests
{
    /// <summary>
    /// 人工初选功能测试 - 主要测试业务逻辑
    /// </summary>
    public class ManualSelectionFeatureTests
    {
        private readonly Mock<ILogger<BlendingCalculationService>> _mockLogger;
        private readonly Mock<IBlendingCalculationService> _mockBlendingService;
        private readonly Mock<IMaterialManagementService> _mockMaterialService;
        private readonly Mock<IPythonAlgorithmService> _mockPythonService;

        public ManualSelectionFeatureTests()
        {
            _mockLogger = new Mock<ILogger<BlendingCalculationService>>();
            _mockBlendingService = new Mock<IBlendingCalculationService>();
            _mockMaterialService = new Mock<IMaterialManagementService>();
            _mockPythonService = new Mock<IPythonAlgorithmService>();
        }

        [Fact]
        public void MaterialSelection_ShouldReturnValidMaterials()
        {
            // Arrange
            var materials = GetTestMaterials();

            // Act & Assert
            Assert.NotEmpty(materials);
            Assert.All(materials, m => Assert.True(m.Id > 0));
            Assert.All(materials, m => Assert.False(string.IsNullOrEmpty(m.Name)));
        }

        [Fact]
        public async Task MaterialService_ShouldLoadMaterials()
        {
            // Arrange
            var materials = GetTestMaterials();
            _mockMaterialService.Setup(x => x.GetAllMaterialsAsync())
                .ReturnsAsync(materials);

            // Act
            var result = await _mockMaterialService.Object.GetAllMaterialsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count());
            _mockMaterialService.Verify(x => x.GetAllMaterialsAsync(), Times.Once);
        }

        [Fact]
        public void MaterialComposition_ShouldHaveValidValues()
        {
            // Arrange
            var materials = GetTestMaterials();

            // Act & Assert
            foreach (var material in materials)
            {
                Assert.True(material.Price > 0, $"Material {material.Name} should have positive price");
                Assert.True(material.CurrentStock >= 0, $"Material {material.Name} should have non-negative stock");
                Assert.False(string.IsNullOrWhiteSpace(material.Name), "Material name should not be empty");
            }
        }

        [Fact]
        public async Task OptimizationCalculation_ShouldReturnValidResult()
        {
            // Arrange
            var expectedResult = new BlendingCalculationResult
            {
                IsSuccess = true,
                CalculationTime = 1500,
                IterationCount = 25,
                ObjectiveValue = 0.001234m,
                BlendingDetails = new List<BlendingDetail>
                {
                    new BlendingDetail { MaterialId = 1, WetRatio = 21.5m },
                    new BlendingDetail { MaterialId = 2, WetRatio = 20.3m },
                    new BlendingDetail { MaterialId = 3, WetRatio = 25.8m }
                }
            };

            _mockBlendingService.Setup(x => x.CalculateBlendingAsync(It.IsAny<BlendingCalculationRequest>()))
                .ReturnsAsync(expectedResult);

            // Act
            var request = new BlendingCalculationRequest
            {
                Mode = BlendingMode.Automatic,
                TargetTFe = 55.0m,
                TargetBasicity = 1.90m,
                TargetMgO = 2.39m,
                TargetAl2O3 = 1.89m,
                AvailableMaterialIds = new List<int> { 1, 2, 3 }
            };

            var result = await _mockBlendingService.Object.CalculateBlendingAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1500, result.CalculationTime);
            Assert.Equal(25, result.IterationCount);
            Assert.Equal(3, result.BlendingDetails.Count());

            // 验证配比总和
            var totalRatio = result.BlendingDetails.Sum(d => d.WetRatio);
            Assert.True(totalRatio > 0, "Total ratio should be positive");
        }

        [Fact]
        public void ConstraintValidation_ShouldValidateParameters()
        {
            // Arrange
            var constraints = GetDefaultConstraints();

            // Act & Assert
            Assert.True(constraints["TFe"].Min < constraints["TFe"].Max);
            Assert.True(constraints["R"].Min < constraints["R"].Max);
            Assert.True(constraints["MgO"].Min < constraints["MgO"].Max);
            Assert.True(constraints["Al2O3"].Min < constraints["Al2O3"].Max);
            Assert.True(constraints["Cost"].Min < constraints["Cost"].Max);
        }

        [Theory]
        [InlineData("印粉海娜", 63.66, 0.10, 4.01, 0.24, 2.42)]
        [InlineData("俄罗斯精粉", 62.95, 1.71, 4.61, 3.70, 2.29)]
        [InlineData("高炉返矿", 55.54, 10.60, 5.59, 2.34, 2.09)]
        public void MaterialComposition_ShouldHaveValidChemicalValues(string materialName, decimal tfe, decimal cao, decimal sio2, decimal mgo, decimal al2o3)
        {
            // Act & Assert
            Assert.False(string.IsNullOrEmpty(materialName));
            Assert.True(tfe > 0, "TFe should be positive");
            Assert.True(cao >= 0, "CaO should be non-negative");
            Assert.True(sio2 > 0, "SiO2 should be positive");
            Assert.True(mgo >= 0, "MgO should be non-negative");
            Assert.True(al2o3 > 0, "Al2O3 should be positive");

            // 验证化学成分的合理范围
            Assert.True(tfe <= 100, "TFe should not exceed 100%");
            Assert.True(cao <= 100, "CaO should not exceed 100%");
            Assert.True(sio2 <= 100, "SiO2 should not exceed 100%");
            Assert.True(mgo <= 100, "MgO should not exceed 100%");
            Assert.True(al2o3 <= 100, "Al2O3 should not exceed 100%");
        }

        [Fact]
        public void BlendingRequest_ShouldHaveValidParameters()
        {
            // Arrange
            var request = new BlendingCalculationRequest
            {
                Mode = BlendingMode.Automatic,
                TargetTFe = 55.0m,
                TargetBasicity = 1.90m,
                TargetMgO = 2.39m,
                TargetAl2O3 = 1.89m,
                AvailableMaterialIds = new List<int> { 1, 2, 3 },
                CostLimit = 665m
            };

            // Act & Assert
            Assert.Equal(BlendingMode.Automatic, request.Mode);
            Assert.True(request.TargetTFe > 0);
            Assert.True(request.TargetBasicity > 0);
            Assert.True(request.TargetMgO > 0);
            Assert.True(request.TargetAl2O3 > 0);
            Assert.NotEmpty(request.AvailableMaterialIds);
            Assert.True(request.CostLimit > 0);
        }

        private List<Material> GetTestMaterials()
        {
            return new List<Material>
            {
                new Material { Id = 1, Name = "印粉海娜", Price = 832.98m, CurrentStock = 1000m, Type = MaterialType.IronOrePowder },
                new Material { Id = 2, Name = "俄罗斯精粉", Price = 772.21m, CurrentStock = 800m, Type = MaterialType.IronOrePowder },
                new Material { Id = 3, Name = "高炉返矿", Price = 550.00m, CurrentStock = 1200m, Type = MaterialType.ReturnOre }
            };
        }

        private Dictionary<string, (decimal Min, decimal Max)> GetDefaultConstraints()
        {
            return new Dictionary<string, (decimal Min, decimal Max)>
            {
                ["TFe"] = (53.5m, 56.5m),
                ["R"] = (1.75m, 2.05m),
                ["MgO"] = (1.8m, 3.0m),
                ["Al2O3"] = (1.5m, 2.5m),
                ["Cost"] = (600m, 665m)
            };
        }
    }
}
