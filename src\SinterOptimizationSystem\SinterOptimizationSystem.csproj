<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>智能优化配料系统</AssemblyTitle>
    <AssemblyDescription>企业级烧结配料智能优化系统客户端</AssemblyDescription>
    <AssemblyCompany>钢铁企业</AssemblyCompany>
    <AssemblyProduct>智能优化配料系统</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SinterOptimizationSystem.Core\SinterOptimizationSystem.Core.csproj" />
    <ProjectReference Include="..\SinterOptimizationSystem.Services\SinterOptimizationSystem.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Production.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
    <Folder Include="data\" />
    <Folder Include="reports\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\py\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>py\%(RecursiveDir)%(Filename)%(Extension)</Link>
    </None>
  </ItemGroup>

</Project>
