using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;namespace SinterOptimizationSystem
{
    /// <summary>
    /// 测试窗体 - 用于验证Windows Forms是否正常工作
    /// </summary>
    public partial class TestForm : Form
    {
        // 控件字段定义
        private GroupBox? targetParamsGroup;
        private GroupBox? processControlGroup;
        private GroupBox? weightSettingsGroup;
        private GroupBox? resultGroup;

        public TestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // 设置窗体属性
            this.Text = "智能优化配料系统 - 测试窗体";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;

            // 创建主面板
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.LightBlue,
                Padding = new Padding(20)
            };

            // 创建标题标签和描述标签
            var titleLabel = new Label
            {
                Text = "智能优化配料系统",
                Font = new Font("Microsoft YaHei", 24, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                AutoSize = true,
                Location = new Point(50, 50)
            };

            var descLabel = new Label
            {
                Text = "基于SQP算法的智能烧结配料优化系统",
                Font = new Font("Microsoft YaHei", 12, FontStyle.Regular),
                ForeColor = Color.DarkGray,
                AutoSize = true,
                Location = new Point(50, 90)
            };

            // 创建目标参数设置区域
            targetParamsGroup = new GroupBox
            {
                Text = "目标参数设置",
                Location = new Point(20, 100),
                Size = new Size(350, 200),
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold)
            };

            // 添加目标参数标签和文本框
            var tfeLabel = new Label { Text = "TFe目标值:", Location = new Point(20, 30), Size = new Size(100, 20) };
            var tfeTextBox = new TextBox { Text = "55.0", Location = new Point(130, 30), Size = new Size(100, 25), Name = "tfeTextBox" };
            var basicityLabel = new Label { Text = "碱度目标值:", Location = new Point(20, 60), Size = new Size(100, 20) };
            var basicityTextBox = new TextBox { Text = "1.90", Location = new Point(130, 60), Size = new Size(100, 25), Name = "basicityTextBox" };
            var mgoLabel = new Label { Text = "MgO目标值:", Location = new Point(20, 90), Size = new Size(100, 20) };
            var mgoTextBox = new TextBox { Text = "2.39", Location = new Point(130, 90), Size = new Size(100, 25), Name = "mgoTextBox" };
            var al2o3Label = new Label { Text = "Al2O3目标值:", Location = new Point(20, 120), Size = new Size(100, 20) };
            var al2o3TextBox = new TextBox { Text = "1.89", Location = new Point(130, 120), Size = new Size(100, 25), Name = "al2o3TextBox" };
            var tfeUnitLabel = new Label { Text = "%", Location = new Point(240, 30), Size = new Size(20, 20) };
            var basicityUnitLabel = new Label { Text = "", Location = new Point(240, 60), Size = new Size(20, 20) };
            var mgoUnitLabel = new Label { Text = "%", Location = new Point(240, 90), Size = new Size(20, 20) };
            var al2o3UnitLabel = new Label { Text = "%", Location = new Point(240, 120), Size = new Size(20, 20) };

            targetParamsGroup.Controls.Add(tfeLabel);
            targetParamsGroup.Controls.Add(tfeTextBox);
            targetParamsGroup.Controls.Add(basicityLabel);
            targetParamsGroup.Controls.Add(basicityTextBox);
            targetParamsGroup.Controls.Add(mgoLabel);
            targetParamsGroup.Controls.Add(mgoTextBox);
            targetParamsGroup.Controls.Add(al2o3Label);
            targetParamsGroup.Controls.Add(al2o3TextBox);
            targetParamsGroup.Controls.Add(tfeUnitLabel);
            targetParamsGroup.Controls.Add(basicityUnitLabel);
            targetParamsGroup.Controls.Add(mgoUnitLabel);
            targetParamsGroup.Controls.Add(al2o3UnitLabel);

            // 创建工艺控制参数区域
            processControlGroup = new GroupBox
            {
                Text = "工艺控制参数",
                Location = new Point(400, 100),
                Size = new Size(350, 200),
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold)
            };

            // 添加工艺控制参数控件
            var carbonLabel = new Label { Text = "配碳量(%):", Location = new Point(20, 30), Size = new Size(100, 20) };
            var carbonTextBox = new TextBox { Text = "4.5", Location = new Point(130, 30), Size = new Size(100, 25), Name = "carbonTextBox" };
            var moistureLabel = new Label { Text = "含水量(%):", Location = new Point(20, 60), Size = new Size(100, 20) };
            var moistureTextBox = new TextBox { Text = "8.0", Location = new Point(130, 60), Size = new Size(100, 25), Name = "moistureTextBox" };
            var operationModeLabel = new Label { Text = "操作模式:", Location = new Point(20, 90), Size = new Size(100, 20) };
            var operationModeCombo = new ComboBox
            {
                Location = new Point(130, 90),
                Size = new Size(150, 25),
                Name = "operationModeCombo",
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            operationModeCombo.Items.AddRange(new object[] { "人工模式", "自动模式", "API模式" });
            operationModeCombo.SelectedIndex = 0;
            var apiStatusLabel = new Label { Text = "API状态:", Location = new Point(20, 120), Size = new Size(100, 20) };
            var apiStatusValueLabel = new Label { Text = "未检查", Location = new Point(130, 120), Size = new Size(100, 20), ForeColor = Color.Red };

            processControlGroup.Controls.Add(carbonLabel);
            processControlGroup.Controls.Add(carbonTextBox);
            processControlGroup.Controls.Add(moistureLabel);
            processControlGroup.Controls.Add(moistureTextBox);
            processControlGroup.Controls.Add(operationModeLabel);
            processControlGroup.Controls.Add(operationModeCombo);
            processControlGroup.Controls.Add(apiStatusLabel);
            processControlGroup.Controls.Add(apiStatusValueLabel);

            // 创建权重设置区域
            weightSettingsGroup = new GroupBox
            {
                Text = "权重设置",
                Location = new Point(20, 320),
                Size = new Size(350, 150),
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold)
            };

            // 添加权重设置控件
            var tfeWeightLabel = new Label { Text = "TFe权重:", Location = new Point(20, 30), Size = new Size(100, 20) };
            var tfeWeightTextBox = new TextBox { Text = "0.5", Location = new Point(130, 30), Size = new Size(100, 25), Name = "tfeWeightTextBox" };
            var basicityWeightLabel = new Label { Text = "碱度权重:", Location = new Point(20, 60), Size = new Size(100, 20) };
            var basicityWeightTextBox = new TextBox { Text = "0.3", Location = new Point(130, 60), Size = new Size(100, 25), Name = "basicityWeightTextBox" };
            var mgoWeightLabel = new Label { Text = "MgO权重:", Location = new Point(20, 90), Size = new Size(100, 20) };
            var mgoWeightTextBox = new TextBox { Text = "0.1", Location = new Point(130, 90), Size = new Size(100, 25), Name = "mgoWeightTextBox" };
            var al2o3WeightLabel = new Label { Text = "Al2O3权重:", Location = new Point(20, 120), Size = new Size(100, 20) };
            var al2o3WeightTextBox = new TextBox { Text = "0.1", Location = new Point(130, 120), Size = new Size(100, 25), Name = "al2o3WeightTextBox" };

            weightSettingsGroup.Controls.Add(tfeWeightLabel);
            weightSettingsGroup.Controls.Add(tfeWeightTextBox);
            weightSettingsGroup.Controls.Add(basicityWeightLabel);
            weightSettingsGroup.Controls.Add(basicityWeightTextBox);
            weightSettingsGroup.Controls.Add(mgoWeightLabel);
            weightSettingsGroup.Controls.Add(mgoWeightTextBox);
            weightSettingsGroup.Controls.Add(al2o3WeightLabel);
            weightSettingsGroup.Controls.Add(al2o3WeightTextBox);

            // 创建优化结果显示区域
            resultGroup = new GroupBox
            {
                Text = "优化计算结果",
                Location = new Point(400, 320),
                Size = new Size(350, 150),
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold)
            };

            // 添加结果显示控件
            var resultTextBox = new TextBox
            {
                Location = new Point(10, 25),
                Size = new Size(330, 110),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Name = "resultTextBox",
                Text = "等待优化计算..."
            };
            resultGroup.Controls.Add(resultTextBox);

            // 创建按钮面板
            var buttonPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Bottom,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // 创建测试按钮
            var testButton = new Button
            {
                Text = "系统测试",
                Size = new Size(120, 40),
                Location = new Point(50, 20),
                BackColor = Color.LightGreen,
                Font = new Font("Microsoft YaHei", 10),
                Name = "testButton"
            };
            testButton.Click += TestButton_Click;

            // 创建关于按钮
            var aboutButton = new Button
            {
                Text = "关于系统",
                Size = new Size(120, 40),
                Location = new Point(200, 20),
                BackColor = Color.LightBlue,
                Font = new Font("Microsoft YaHei", 10),
                Name = "aboutButton"
            };
            aboutButton.Click += AboutButton_Click;

            // 创建退出按钮
            var exitButton = new Button
            {
                Text = "退出",
                Size = new Size(120, 40),
                Location = new Point(350, 20),
                BackColor = Color.LightGray,
                Font = new Font("Microsoft YaHei", 10)
            };
            exitButton.Click += ExitButton_Click;

            // 添加控件到面板
            mainPanel.Controls.Add(titleLabel);
            mainPanel.Controls.Add(descLabel);
            mainPanel.Controls.Add(targetParamsGroup);
            mainPanel.Controls.Add(processControlGroup);
            mainPanel.Controls.Add(weightSettingsGroup);
            mainPanel.Controls.Add(resultGroup);
            
            buttonPanel.Controls.Add(testButton);
            buttonPanel.Controls.Add(aboutButton);
            buttonPanel.Controls.Add(exitButton);

            // 添加面板到窗体
            this.Controls.Add(mainPanel);
            this.Controls.Add(buttonPanel);

            // 设置图标
            try
            {
                this.Icon = SystemIcons.Application;
            }
            catch
            {
                // 忽略图标设置错误
            }
        }

        private void TestButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show(
                "系统功能测试：\n\n" +
                "✅ Windows Forms界面正常\n" +
                "✅ 事件处理正常\n" +
                "✅ 控件显示正常\n" +
                "✅ 消息框正常\n\n" +
                "系统已准备就绪！",
                "功能测试",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private void AboutButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show(
                "智能烧结配料优化系统\n\n" +
                "版本: v1.0.0\n" +
                "基于SQP算法的智能优化\n" +
                "支持C#客户端调用Python算法\n\n" +
                "开发者: 智能优化团队\n" +
                "技术支持: AI辅助开发",
                "关于系统",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            var resultTextBox = resultGroup?.Controls["resultTextBox"] as TextBox;
            if (resultTextBox != null)
            {
                resultTextBox.Text = "等待优化计算...";
            }
        }

        private async void RunOptimizationButton_Click(object? sender, EventArgs e)
        {
            // 获取界面输入参数并进行空值检查
            var tfeTextBox = targetParamsGroup.Controls["tfeTextBox"] as TextBox;
            var basicityTextBox = targetParamsGroup.Controls["basicityTextBox"] as TextBox;
            var mgoTextBox = targetParamsGroup.Controls["mgoTextBox"] as TextBox;
            var al2o3TextBox = targetParamsGroup.Controls["al2o3TextBox"] as TextBox;

            var carbonTextBox = processControlGroup.Controls["carbonTextBox"] as TextBox;
            var moistureTextBox = processControlGroup.Controls["moistureTextBox"] as TextBox;
            var operationModeCombo = processControlGroup.Controls["operationModeCombo"] as ComboBox;
            var apiStatusLabel = processControlGroup.Controls["apiStatusValueLabel"] as Label;

            var tfeWeightTextBox = weightSettingsGroup.Controls["tfeWeightTextBox"] as TextBox;
            var basicityWeightTextBox = weightSettingsGroup.Controls["basicityWeightTextBox"] as TextBox;
            var mgoWeightTextBox = weightSettingsGroup.Controls["mgoWeightTextBox"] as TextBox;
            var al2o3WeightTextBox = weightSettingsGroup.Controls["al2o3WeightTextBox"] as TextBox;

            var resultTextBox = resultGroup.Controls["resultTextBox"] as TextBox;

            if (tfeTextBox == null || basicityTextBox == null || mgoTextBox == null || al2o3TextBox == null ||
                carbonTextBox == null || moistureTextBox == null || operationModeCombo == null ||
                tfeWeightTextBox == null || basicityWeightTextBox == null || mgoWeightTextBox == null ||
                al2o3WeightTextBox == null || apiStatusLabel == null || resultTextBox == null)
            {
                MessageBox.Show("界面控件初始化不完整，请重启应用程序", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                // 解析数值参数
                var tfeTarget = double.Parse(tfeTextBox.Text);
                var basicityTarget = double.Parse(basicityTextBox.Text);
                var mgoTarget = double.Parse(mgoTextBox.Text);
                var al2o3Target = double.Parse(al2o3TextBox.Text);

                var carbonContent = double.Parse(carbonTextBox.Text);
                var moistureContent = double.Parse(moistureTextBox.Text);
                var operationMode = operationModeCombo.SelectedItem?.ToString() ?? "人工模式";

                var tfeWeight = double.Parse(tfeWeightTextBox.Text);
                var basicityWeight = double.Parse(basicityWeightTextBox.Text);
                var mgoWeight = double.Parse(mgoWeightTextBox.Text);
                var al2o3Weight = double.Parse(al2o3WeightTextBox.Text);

                // 更新API状态
                apiStatusLabel.Text = "检查中...";
                apiStatusLabel.ForeColor = Color.Orange;
                this.Refresh();

                // 创建Python算法服务实例 - 使用依赖注入容器
                var logger = new Microsoft.Extensions.Logging.LoggerFactory().CreateLogger<SinterOptimizationSystem.Services.PythonAlgorithmService>();
                var configuration = new Microsoft.Extensions.Configuration.ConfigurationBuilder().Build();
                var httpClient = new System.Net.Http.HttpClient();
                var pythonService = new SinterOptimizationSystem.Services.PythonAlgorithmService(logger, configuration, httpClient);

                // 准备优化参数 - 转换为PythonAlgorithmInput格式
                var input = new SinterOptimizationSystem.Core.Interfaces.PythonAlgorithmInput
                {
                    MaterialNames = new List<string> { "印粉海娜", "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "生石灰", "轻烧白云石", "焦粉", "澳粉纵横" },
                    MaterialsMatrix = new decimal[9, 8] // 9种物料，8种化学成分
                    {
                        { 64.5m, 0.1m, 4.2m, 0.1m, 1.8m, 8.5m, 2.5m, 650m }, // 印粉海娜
                        { 65.2m, 0.2m, 3.8m, 0.2m, 1.5m, 9.0m, 2.0m, 680m }, // 俄罗斯精粉
                        { 55.8m, 10.5m, 5.2m, 2.8m, 2.1m, 5.5m, 12.5m, 280m }, // 高炉返矿
                        { 58.5m, 8.2m, 4.8m, 2.2m, 1.9m, 6.2m, 15.8m, 320m }, // 回收料
                        { 15.2m, 45.8m, 12.5m, 8.5m, 2.8m, 3.2m, 5.5m, 120m }, // 钢渣
                        { 0.5m, 92.5m, 2.5m, 1.8m, 1.2m, 0.5m, 1.5m, 450m }, // 生石灰
                        { 2.8m, 32.5m, 5.8m, 19.5m, 1.5m, 0.8m, 32.5m, 380m }, // 轻烧白云石
                        { 1.2m, 1.5m, 6.8m, 0.5m, 2.8m, 8.5m, 78.5m, 1200m }, // 焦粉
                        { 62.8m, 0.3m, 4.5m, 0.2m, 2.2m, 9.2m, 3.5m, 620m }  // 澳粉纵横
                    },
                    ManualSelection = new bool[9] { true, true, true, true, true, true, true, true, true },
                    Targets = new Dictionary<string, decimal>
                    {
                        { "TFe", (decimal)tfeTarget },
                        { "R", (decimal)basicityTarget },
                        { "MgO", (decimal)mgoTarget },
                        { "Al2O3", (decimal)al2o3Target }
                    },
                    Weights = new Dictionary<string, decimal>
                    {
                        { "TFe", (decimal)tfeWeight },
                        { "R", (decimal)basicityWeight },
                        { "MgO", (decimal)mgoWeight },
                        { "Al2O3", (decimal)al2o3Weight }
                    },
                    InitialRatios = new decimal[9] { 11.11m, 11.11m, 11.11m, 11.11m, 11.11m, 11.11m, 11.11m, 11.11m, 11.11m },
                    BoundsMatrix = new decimal[9, 2] // 每种物料的最小和最大配比
                    {
                        { 5.0m, 25.0m }, { 5.0m, 25.0m }, { 5.0m, 20.0m }, { 5.0m, 20.0m },
                        { 2.0m, 15.0m }, { 2.0m, 15.0m }, { 2.0m, 15.0m }, { 2.0m, 10.0m }, { 5.0m, 25.0m }
                    },
                    MinWetRatioThreshold = 2.0m,
                    MaxIterations = 500,
                    Tolerance = 1e-7m
                };

                // 调用优化算法
                resultTextBox.Text = "正在运行SQP优化算法...\n请稍候...";
                this.Refresh();

                var result = await pythonService.RunSQPOptimizationAsync(input);

                // 更新API状态
                apiStatusLabel.Text = "正常"; 
                apiStatusLabel.ForeColor = Color.Green;

                // 格式化输出结果
                var resultText = FormatOptimizationResult(result, tfeTarget, basicityTarget, mgoTarget, al2o3Target, 
                    tfeWeight, basicityWeight, mgoWeight, al2o3Weight, carbonContent, moistureContent, operationMode);

                resultTextBox.Text = resultText;
            }
            catch (Exception ex)
            {
                // 更新API状态
                apiStatusLabel.Text = "异常"; 
                apiStatusLabel.ForeColor = Color.Red;

                // 显示错误信息
                resultTextBox.Text = $"优化计算失败: {ex.Message}\n\n{ex.StackTrace}";
            }
            finally
            {
                this.Refresh();
            }
        }

        private string FormatOptimizationResult(SinterOptimizationSystem.Core.Interfaces.PythonAlgorithmOutput result, 
            double tfeTarget, double basicityTarget, double mgoTarget, double al2o3Target,
            double tfeWeight, double basicityWeight, double mgoWeight, double al2o3Weight,
            double carbonContent, double moistureContent, string operationMode)
        {
            if (!result.IsSuccess)
            {
                return $"============================================\n" +
                       "❌ 优化计算失败\n" +
                       "============================================\n" +
                       $"错误信息: {result.ErrorMessage}";
            }

            var sb = new StringBuilder();
            sb.AppendLine("============================================");
            sb.AppendLine("✅ 智能烧结配料优化算法完成");
            sb.AppendLine("============================================");
            sb.AppendLine($"🎯 目标化学成分: TFe={tfeTarget}%, R={basicityTarget}, MgO={mgoTarget}%, Al2O3={al2o3Target}%");
            sb.AppendLine($"⚖️ 权重设置: TFe={tfeWeight}, R={basicityWeight}, MgO={mgoWeight}, Al2O3={al2o3Weight}");
            sb.AppendLine($"⚙️ 工艺参数: 配碳量={carbonContent}%, 含水量={moistureContent}%, 操作模式={operationMode}");
            sb.AppendLine();
            sb.AppendLine("📋 优化结果:");
            sb.AppendLine();

            string[] materialNames = { "印粉海娜", "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "生石灰", "轻烧白云石", "焦粉", "澳粉纵横" };
            for (int i = 0; i < result.OptimalRatios.Length && i < materialNames.Length; i++)
            {
                sb.AppendLine($"   {materialNames[i]}: {result.OptimalRatios[i]:F2}%");
            }

            sb.AppendLine();
            sb.AppendLine("📊 最终化学成分:");
            foreach (var prop in result.FinalProperties)
            {
                sb.AppendLine($"   {prop.Key}: {prop.Value:F2}%");
            }

            sb.AppendLine();
            sb.AppendLine($"🎯 目标函数值: {result.ObjectiveValue:F6}");
            sb.AppendLine($"🔄 迭代次数: {result.IterationCount}");
            sb.AppendLine($"⏱️ 计算耗时: {result.CalculationTime}ms");

            return sb.ToString();
        }

        private void ExitButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "确定要退出智能优化配料系统吗？",
                "确认退出",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }



        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "确定要关闭系统吗？",
                "确认关闭",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }

            base.OnFormClosing(e);
        }
    }
}
