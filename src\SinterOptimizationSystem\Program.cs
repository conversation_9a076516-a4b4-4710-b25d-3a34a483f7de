using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Services;
using System.Text;

namespace SinterOptimizationSystem
{
    /// <summary>
    /// 应用程序主入口
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 配置应用程序
                ApplicationConfiguration.Initialize();

                // 创建主机构建器
                var hostBuilder = CreateHostBuilder();
                using var host = hostBuilder.Build();

                // 启动服务
                host.Start();

                // 获取服务提供者
                var serviceProvider = host.Services;

                // 创建并运行主窗体
                var mainForm = serviceProvider.GetRequiredService<MainForm>();
                Application.Run(mainForm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 创建主机构建器
        /// </summary>
        private static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.SetBasePath(AppDomain.CurrentDomain.BaseDirectory);
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                    config.AddEnvironmentVariables();
                })
                .ConfigureLogging((context, logging) =>
                {
                    logging.ClearProviders();
                    logging.AddConfiguration(context.Configuration.GetSection("Logging"));
                    logging.AddConsole();
                    logging.AddDebug();
                    
                    // 添加文件日志
                    var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                    if (!Directory.Exists(logPath))
                    {
                        Directory.CreateDirectory(logPath);
                    }
                })
                .ConfigureServices((context, services) =>
                {
                    ConfigureServices(services, context.Configuration);
                })
                .UseConsoleLifetime();
        }

        /// <summary>
        /// 配置依赖注入服务
        /// </summary>
        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // 注册配置
            services.AddSingleton(configuration);

            // 注册核心服务
            services.AddScoped<IMaterialManagementService, MaterialManagementService>();
            services.AddScoped<IBlendingCalculationService, BlendingCalculationService>();
            services.AddScoped<IPythonAlgorithmService, PythonAlgorithmService>();
            services.AddScoped<IMaterialFlowService, MaterialFlowService>();

            // 注册窗体
            services.AddTransient<MainForm>();
            services.AddTransient<MaterialManagementForm>();
            services.AddTransient<BlendingCalculationForm>();
            services.AddTransient<MaterialFlowTrackingForm>();
            services.AddTransient<PredictionAnalysisForm>();
            services.AddTransient<SystemConfigurationForm>();

            // 注册其他服务
            services.AddSingleton<IApplicationService, ApplicationService>();
            services.AddSingleton<IConfigurationService, ConfigurationService>();

            // HTTP客户端
            services.AddHttpClient();

            // 内存缓存
            services.AddMemoryCache();
        }
    }

    /// <summary>
    /// 应用程序服务接口
    /// </summary>
    public interface IApplicationService
    {
        /// <summary>
        /// 初始化应用程序
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// 关闭应用程序
        /// </summary>
        Task ShutdownAsync();

        /// <summary>
        /// 获取应用程序信息
        /// </summary>
        ApplicationInfo GetApplicationInfo();
    }

    /// <summary>
    /// 应用程序信息
    /// </summary>
    public class ApplicationInfo
    {
        public string Name { get; set; } = "智能优化配料系统";
        public string Version { get; set; } = "1.0.0";
        public string Company { get; set; } = "钢铁企业";
        public DateTime StartTime { get; set; }
        public string Environment { get; set; } = "Development";
    }

    /// <summary>
    /// 应用程序服务实现
    /// </summary>
    public class ApplicationService : IApplicationService
    {
        private readonly ILogger<ApplicationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ApplicationInfo _applicationInfo;

        public ApplicationService(ILogger<ApplicationService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _applicationInfo = new ApplicationInfo
            {
                StartTime = DateTime.Now,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"
            };
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化应用程序...");

                // 检查Python环境
                await CheckPythonEnvironmentAsync();

                // 初始化数据库连接
                await InitializeDatabaseAsync();

                // 加载配置
                await LoadConfigurationAsync();

                _logger.LogInformation("应用程序初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用程序初始化失败");
                throw;
            }
        }

        public async Task ShutdownAsync()
        {
            try
            {
                _logger.LogInformation("正在关闭应用程序...");

                // 保存配置
                await SaveConfigurationAsync();

                // 清理资源
                await CleanupResourcesAsync();

                _logger.LogInformation("应用程序关闭完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用程序关闭异常");
            }
        }

        public ApplicationInfo GetApplicationInfo()
        {
            return _applicationInfo;
        }

        private async Task CheckPythonEnvironmentAsync()
        {
            _logger.LogInformation("检查Python环境...");
            // 实际检查逻辑
            await Task.Delay(100);
        }

        private async Task InitializeDatabaseAsync()
        {
            _logger.LogInformation("初始化数据库连接...");
            // 实际数据库初始化逻辑
            await Task.Delay(100);
        }

        private async Task LoadConfigurationAsync()
        {
            _logger.LogInformation("加载配置...");
            // 实际配置加载逻辑
            await Task.Delay(100);
        }

        private async Task SaveConfigurationAsync()
        {
            _logger.LogInformation("保存配置...");
            // 实际配置保存逻辑
            await Task.Delay(100);
        }

        private async Task CleanupResourcesAsync()
        {
            _logger.LogInformation("清理资源...");
            // 实际资源清理逻辑
            await Task.Delay(100);
        }
    }

    /// <summary>
    /// 配置服务接口
    /// </summary>
    public interface IConfigurationService
    {
        T GetValue<T>(string key, T defaultValue = default!);
        void SetValue<T>(string key, T value);
        Task SaveAsync();
        Task LoadAsync();
    }

    /// <summary>
    /// 配置服务实现
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        private readonly ILogger<ConfigurationService> _logger;
        private readonly Dictionary<string, object> _settings;
        private readonly string _configFilePath;

        public ConfigurationService(ILogger<ConfigurationService> logger)
        {
            _logger = logger;
            _settings = new Dictionary<string, object>();
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "user_settings.json");
        }

        public T GetValue<T>(string key, T defaultValue = default!)
        {
            try
            {
                if (_settings.TryGetValue(key, out var value) && value is T typedValue)
                {
                    return typedValue;
                }
                return defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置值失败: {Key}", key);
                return defaultValue;
            }
        }

        public void SetValue<T>(string key, T value)
        {
            try
            {
                _settings[key] = value!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置配置值失败: {Key}", key);
            }
        }

        public async Task SaveAsync()
        {
            try
            {
                var json = System.Text.Json.JsonSerializer.Serialize(_settings, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });
                await File.WriteAllTextAsync(_configFilePath, json);
                _logger.LogInformation("配置保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置失败");
            }
        }

        public async Task LoadAsync()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = await File.ReadAllTextAsync(_configFilePath);
                    var settings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                    if (settings != null)
                    {
                        _settings.Clear();
                        foreach (var setting in settings)
                        {
                            _settings[setting.Key] = setting.Value;
                        }
                    }
                    _logger.LogInformation("配置加载成功");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载配置失败");
            }
        }
    }
}
