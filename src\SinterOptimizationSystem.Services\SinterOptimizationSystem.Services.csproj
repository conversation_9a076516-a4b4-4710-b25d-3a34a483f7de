<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>智能优化配料系统服务层</AssemblyTitle>
    <AssemblyDescription>智能优化配料系统业务服务和外部集成</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SinterOptimizationSystem.Core\SinterOptimizationSystem.Core.csproj" />
  </ItemGroup>

</Project>
