using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem
{
    /// <summary>
    /// 配料计算窗体
    /// </summary>
    public partial class BlendingCalculationForm : Form
    {
        private readonly ILogger<BlendingCalculationForm> _logger;
        private readonly IBlendingCalculationService _blendingService;
        private readonly IMaterialManagementService _materialService;
        private readonly IPythonAlgorithmService _pythonService;

        public BlendingCalculationForm(
            ILogger<BlendingCalculationForm> logger,
            IBlendingCalculationService blendingService,
            IMaterialManagementService materialService,
            IPythonAlgorithmService pythonService)
        {
            _logger = logger;
            _blendingService = blendingService;
            _materialService = materialService;
            _pythonService = pythonService;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Text = "智能配料计算系统";
            Size = new Size(1600, 1000);
            StartPosition = FormStartPosition.CenterParent;
            WindowState = FormWindowState.Maximized;

            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // 设置列宽
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F)); // 左侧参数
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F)); // 中间原料
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F)); // 右侧结果

            // 设置行高
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));  // 标题
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 60F));   // 主要内容
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));   // 结果显示
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));  // 按钮

            // 标题栏
            var titlePanel = CreateTitlePanel();
            mainPanel.SetColumnSpan(titlePanel, 3);
            mainPanel.Controls.Add(titlePanel, 0, 0);

            // 左侧：目标参数设置
            var parameterPanel = CreateParameterPanel();
            mainPanel.Controls.Add(parameterPanel, 0, 1);

            // 中间：原料成分和配比
            var materialPanel = CreateMaterialPanel();
            mainPanel.Controls.Add(materialPanel, 1, 1);

            // 右侧：实时监控
            var monitorPanel = CreateMonitorPanel();
            mainPanel.Controls.Add(monitorPanel, 2, 1);

            // 底部结果显示区域
            var resultPanel = CreateResultPanel();
            mainPanel.SetColumnSpan(resultPanel, 3);
            mainPanel.Controls.Add(resultPanel, 0, 2);

            // 底部按钮面板
            var buttonPanel = CreateButtonPanel();
            mainPanel.SetColumnSpan(buttonPanel, 3);
            mainPanel.Controls.Add(buttonPanel, 0, 3);

            Controls.Add(mainPanel);
        }

        private Panel CreateTitlePanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(70, 130, 180)
            };

            var titleLabel = new Label
            {
                Text = "智能烧结配料优化计算系统",
                Font = new Font("Microsoft YaHei", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            var statusLabel = new Label
            {
                Text = "系统状态: 就绪",
                Font = new Font("Microsoft YaHei", 10F),
                ForeColor = Color.LightGray,
                Name = "SystemStatus",
                Location = new Point(10, 50),
                Size = new Size(200, 20)
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(statusLabel);
            return panel;
        }

        private GroupBox CreateParameterPanel()
        {
            var panel = new GroupBox
            {
                Text = "目标参数设置",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 12,
                Padding = new Padding(10)
            };

            // 设置列宽
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 45F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 55F));

            int row = 0;

            // 目标成分设置
            var targetGroupLabel = new Label
            {
                Text = "目标成分指标",
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleLeft
            };
            layout.SetColumnSpan(targetGroupLabel, 2);
            layout.Controls.Add(targetGroupLabel, 0, row++);

            // TFe目标值
            layout.Controls.Add(new Label { Text = "TFe目标值(%):", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var tfeTextBox = new TextBox { Text = "55.0", Name = "TFeTarget", Width = 80 };
            layout.Controls.Add(tfeTextBox, 1, row++);

            // 碱度目标值
            layout.Controls.Add(new Label { Text = "碱度目标值:", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var basicityTextBox = new TextBox { Text = "1.90", Name = "BasicityTarget", Width = 80 };
            layout.Controls.Add(basicityTextBox, 1, row++);

            // MgO目标值
            layout.Controls.Add(new Label { Text = "MgO目标值(%):", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var mgoTextBox = new TextBox { Text = "2.39", Name = "MgOTarget", Width = 80 };
            layout.Controls.Add(mgoTextBox, 1, row++);

            // Al2O3目标值
            layout.Controls.Add(new Label { Text = "Al2O3目标值(%):", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var al2o3TextBox = new TextBox { Text = "1.89", Name = "Al2O3Target", Width = 80 };
            layout.Controls.Add(al2o3TextBox, 1, row++);

            // 分隔线
            row++;

            // 工艺参数设置
            var processGroupLabel = new Label
            {
                Text = "工艺控制参数",
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleLeft
            };
            layout.SetColumnSpan(processGroupLabel, 2);
            layout.Controls.Add(processGroupLabel, 0, row++);

            // 配碳量
            layout.Controls.Add(new Label { Text = "配碳量(%):", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var carbonTextBox = new TextBox { Text = "4.5", Name = "CarbonTarget", Width = 80 };
            layout.Controls.Add(carbonTextBox, 1, row++);

            // 含水量
            layout.Controls.Add(new Label { Text = "含水量(%):", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var moistureTextBox = new TextBox { Text = "8.0", Name = "MoistureTarget", Width = 80 };
            layout.Controls.Add(moistureTextBox, 1, row++);

            // 操作模式
            layout.Controls.Add(new Label { Text = "操作模式:", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var modeComboBox = new ComboBox
            {
                Name = "OperationMode",
                Width = 120,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            modeComboBox.Items.AddRange(new[] { "人工模式", "自动模式" });
            modeComboBox.SelectedIndex = 0;
            layout.Controls.Add(modeComboBox, 1, row++);

            // API状态
            layout.Controls.Add(new Label { Text = "API状态:", TextAlign = ContentAlignment.MiddleRight }, 0, row);
            var statusLabel = new Label { Text = "未检查", Name = "ApiStatus", ForeColor = Color.Gray, Width = 120 };
            layout.Controls.Add(statusLabel, 1, row++);

            panel.Controls.Add(layout);
            return panel;
        }

        private GroupBox CreateMaterialPanel()
        {
            var panel = new GroupBox
            {
                Text = "原料成分与配比",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            // 原料成分标签页
            var compositionTab = new TabPage("原料成分");
            var compositionGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Name = "MaterialCompositionGrid",
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // 添加列
            compositionGrid.Columns.Add("MaterialName", "原料名称");
            compositionGrid.Columns.Add("TFe", "TFe(%)");
            compositionGrid.Columns.Add("CaO", "CaO(%)");
            compositionGrid.Columns.Add("SiO2", "SiO2(%)");
            compositionGrid.Columns.Add("MgO", "MgO(%)");
            compositionGrid.Columns.Add("Al2O3", "Al2O3(%)");
            compositionGrid.Columns.Add("S", "S(%)");
            compositionGrid.Columns.Add("P", "P(%)");
            compositionGrid.Columns.Add("Cost", "成本(元/吨)");
            compositionGrid.Columns.Add("Selected", "选用");

            // 添加默认数据
            var materials = new[]
            {
                new object[] { "印粉海娜", 63.66, 0.10, 4.01, 0.24, 2.42, 0.02, 0.05, 832.98, true },
                new object[] { "俄罗斯精粉", 62.95, 1.71, 4.61, 3.70, 2.29, 0.03, 0.04, 772.21, true },
                new object[] { "高炉返矿", 55.54, 10.60, 5.59, 2.34, 2.09, 0.15, 0.08, 550.00, true },
                new object[] { "回收料", 56.16, 6.56, 6.31, 2.39, 2.51, 0.12, 0.06, 100.00, true },
                new object[] { "钢渣", 26.46, 28.15, 15.43, 2.79, 2.53, 0.25, 0.15, 550.00, true },
                new object[] { "生石灰", 0.00, 71.74, 3.52, 2.28, 1.19, 0.05, 0.02, 219.00, true },
                new object[] { "轻烧白云石", 0.00, 42.67, 5.31, 26.12, 0.10, 0.03, 0.01, 183.76, true },
                new object[] { "焦粉", 0.19, 0.37, 8.82, 0.22, 3.31, 0.65, 0.02, 520.00, true },
                new object[] { "澳粉纵横", 60.80, 0.10, 4.35, 0.20, 2.30, 0.02, 0.04, 832.98, true }
            };

            foreach (var material in materials)
            {
                compositionGrid.Rows.Add(material);
            }

            compositionTab.Controls.Add(compositionGrid);
            tabControl.TabPages.Add(compositionTab);

            // 配比方案标签页
            var ratioTab = new TabPage("配比方案");
            var ratioGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Name = "MaterialRatioGrid",
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            ratioGrid.Columns.Add("MaterialName", "原料名称");
            ratioGrid.Columns.Add("CurrentRatio", "当前配比(%)");
            ratioGrid.Columns.Add("OptimalRatio", "优化配比(%)");
            ratioGrid.Columns.Add("MinRatio", "最小配比(%)");
            ratioGrid.Columns.Add("MaxRatio", "最大配比(%)");
            ratioGrid.Columns.Add("FeedRate", "下料量(t/h)");

            // 添加配比数据
            var ratios = new[]
            {
                new object[] { "印粉海娜", 21.0, 0.0, 15.0, 25.0, 0.0 },
                new object[] { "俄罗斯精粉", 20.0, 0.0, 15.0, 25.0, 0.0 },
                new object[] { "高炉返矿", 25.0, 0.0, 20.0, 30.0, 0.0 },
                new object[] { "回收料", 7.0, 0.0, 5.0, 10.0, 0.0 },
                new object[] { "钢渣", 4.0, 0.0, 2.0, 6.0, 0.0 },
                new object[] { "生石灰", 6.0, 0.0, 5.0, 8.0, 0.0 },
                new object[] { "轻烧白云石", 4.0, 0.0, 2.0, 5.0, 0.0 },
                new object[] { "焦粉", 4.0, 0.0, 3.0, 5.0, 0.0 },
                new object[] { "澳粉纵横", 9.0, 0.0, 8.0, 15.0, 0.0 }
            };

            foreach (var ratio in ratios)
            {
                ratioGrid.Rows.Add(ratio);
            }

            ratioTab.Controls.Add(ratioGrid);
            tabControl.TabPages.Add(ratioTab);

            panel.Controls.Add(tabControl);
            return panel;
        }

        private GroupBox CreateMonitorPanel()
        {
            var panel = new GroupBox
            {
                Text = "实时监控",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // 设置行高
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));

            // 系统状态监控
            var statusGroup = new GroupBox
            {
                Text = "系统状态",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            var statusText = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Name = "SystemStatusText",
                Text = "系统运行正常\n算法服务: 连接中...\n数据库: 连接正常\n最后更新: " + DateTime.Now.ToString("HH:mm:ss")
            };
            statusGroup.Controls.Add(statusText);
            layout.Controls.Add(statusGroup, 0, 0);

            // 质量指标监控
            var qualityGroup = new GroupBox
            {
                Text = "质量指标",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            var qualityText = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Name = "QualityMonitorText",
                Text = "TFe: -- %\n碱度: --\nMgO: -- %\nAl2O3: -- %\n转鼓指数: -- %"
            };
            qualityGroup.Controls.Add(qualityText);
            layout.Controls.Add(qualityGroup, 0, 1);

            // 生产监控
            var productionGroup = new GroupBox
            {
                Text = "生产监控",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            var productionText = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Name = "ProductionMonitorText",
                Text = "产量: -- t/h\n配料速度: -- t/h\n混匀矿仓位: --\n下料异常: 无"
            };
            productionGroup.Controls.Add(productionText);
            layout.Controls.Add(productionGroup, 0, 2);

            // 报警信息
            var alarmGroup = new GroupBox
            {
                Text = "报警信息",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            var alarmText = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Name = "AlarmText",
                Text = "无报警信息",
                BackColor = Color.LightGreen
            };
            alarmGroup.Controls.Add(alarmText);
            layout.Controls.Add(alarmGroup, 0, 3);

            panel.Controls.Add(layout);
            return panel;
        }

        private GroupBox CreateResultPanel()
        {
            var panel = new GroupBox
            {
                Text = "优化计算结果",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold)
            };

            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            // 计算结果标签页
            var resultTab = new TabPage("计算结果");
            var resultTextBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                ReadOnly = true,
                Name = "ResultTextBox",
                Font = new Font("Consolas", 9F),
                Text = "等待优化计算...\n\n操作说明：\n1. 设置目标参数\n2. 检查API连接\n3. 点击开始优化\n4. 查看计算结果"
            };
            resultTab.Controls.Add(resultTextBox);
            tabControl.TabPages.Add(resultTab);

            // 历史记录标签页
            var historyTab = new TabPage("历史记录");
            var historyGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Name = "HistoryGrid",
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            historyGrid.Columns.Add("Time", "计算时间");
            historyGrid.Columns.Add("TFe", "TFe目标");
            historyGrid.Columns.Add("Basicity", "碱度目标");
            historyGrid.Columns.Add("Result", "计算结果");
            historyGrid.Columns.Add("Duration", "耗时(ms)");

            historyTab.Controls.Add(historyGrid);
            tabControl.TabPages.Add(historyTab);

            panel.Controls.Add(tabControl);
            return panel;
        }

        private Panel CreateButtonPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            var buttonLayout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(10),
                WrapContents = false
            };

            // 系统控制按钮组
            var systemGroup = new GroupBox
            {
                Text = "系统控制",
                Size = new Size(200, 60),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var checkApiButton = new Button
            {
                Text = "检查API连接",
                Size = new Size(80, 30),
                Location = new Point(10, 20),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei", 8F)
            };
            checkApiButton.Click += CheckApiButton_Click;

            var refreshButton = new Button
            {
                Text = "刷新数据",
                Size = new Size(80, 30),
                Location = new Point(100, 20),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei", 8F)
            };
            refreshButton.Click += RefreshButton_Click;

            systemGroup.Controls.Add(checkApiButton);
            systemGroup.Controls.Add(refreshButton);
            buttonLayout.Controls.Add(systemGroup);

            // 配料计算按钮组
            var calculationGroup = new GroupBox
            {
                Text = "配料计算",
                Size = new Size(300, 60),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var optimizeButton = new Button
            {
                Text = "开始优化",
                Size = new Size(80, 30),
                Location = new Point(10, 20),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen,
                Font = new Font("Microsoft YaHei", 8F, FontStyle.Bold)
            };
            optimizeButton.Click += OptimizeButton_Click;

            var stopButton = new Button
            {
                Text = "停止计算",
                Size = new Size(80, 30),
                Location = new Point(100, 20),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightCoral,
                Font = new Font("Microsoft YaHei", 8F)
            };
            stopButton.Click += StopButton_Click;

            var confirmButton = new Button
            {
                Text = "配比确认",
                Size = new Size(80, 30),
                Location = new Point(190, 20),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightBlue,
                Font = new Font("Microsoft YaHei", 8F)
            };
            confirmButton.Click += ConfirmButton_Click;

            calculationGroup.Controls.Add(optimizeButton);
            calculationGroup.Controls.Add(stopButton);
            calculationGroup.Controls.Add(confirmButton);
            buttonLayout.Controls.Add(calculationGroup);

            // 数据管理按钮组
            var dataGroup = new GroupBox
            {
                Text = "数据管理",
                Size = new Size(250, 60),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var saveButton = new Button
            {
                Text = "保存方案",
                Size = new Size(70, 30),
                Location = new Point(10, 20),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei", 8F)
            };
            saveButton.Click += SaveButton_Click;

            var loadButton = new Button
            {
                Text = "加载方案",
                Size = new Size(70, 30),
                Location = new Point(90, 20),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei", 8F)
            };
            loadButton.Click += LoadButton_Click;

            var exportButton = new Button
            {
                Text = "导出报告",
                Size = new Size(70, 30),
                Location = new Point(170, 20),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei", 8F)
            };
            exportButton.Click += ExportButton_Click;

            dataGroup.Controls.Add(saveButton);
            dataGroup.Controls.Add(loadButton);
            dataGroup.Controls.Add(exportButton);
            buttonLayout.Controls.Add(dataGroup);

            // 模式切换按钮组
            var modeGroup = new GroupBox
            {
                Text = "操作模式",
                Size = new Size(180, 60),
                Font = new Font("Microsoft YaHei", 9F)
            };

            var manualModeButton = new RadioButton
            {
                Text = "人工模式",
                Size = new Size(80, 25),
                Location = new Point(10, 20),
                Checked = true,
                Font = new Font("Microsoft YaHei", 8F)
            };

            var autoModeButton = new RadioButton
            {
                Text = "自动模式",
                Size = new Size(80, 25),
                Location = new Point(90, 20),
                Font = new Font("Microsoft YaHei", 8F)
            };

            modeGroup.Controls.Add(manualModeButton);
            modeGroup.Controls.Add(autoModeButton);
            buttonLayout.Controls.Add(modeGroup);

            panel.Controls.Add(buttonLayout);
            return panel;
        }

        private async void CheckApiButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var statusLabel = Controls.Find("ApiStatus", true).FirstOrDefault() as Label;
                var resultTextBox = Controls.Find("ResultTextBox", true).FirstOrDefault() as TextBox;

                if (statusLabel != null)
                {
                    statusLabel.Text = "检查中...";
                    statusLabel.ForeColor = Color.Orange;
                }

                if (resultTextBox != null)
                {
                    resultTextBox.Text = "正在检查Python API服务连接...\n";
                }

                var result = await _pythonService.CheckPythonEnvironmentAsync();

                if (statusLabel != null)
                {
                    statusLabel.Text = result.IsAvailable ? "连接正常" : "连接失败";
                    statusLabel.ForeColor = result.IsAvailable ? Color.Green : Color.Red;
                }

                if (resultTextBox != null)
                {
                    resultTextBox.Text = $"API连接检查结果:\n" +
                                       $"状态: {(result.IsAvailable ? "正常" : "失败")}\n" +
                                       $"版本: {result.Version}\n" +
                                       $"错误信息: {string.Join(", ", result.MissingPackages)}\n\n" +
                                       $"检查时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"检查API连接时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void OptimizeButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var resultTextBox = Controls.Find("ResultTextBox", true).FirstOrDefault() as TextBox;
                var systemStatusText = Controls.Find("SystemStatusText", true).FirstOrDefault() as TextBox;
                var ratioGrid = Controls.Find("MaterialRatioGrid", true).FirstOrDefault() as DataGridView;
                var historyGrid = Controls.Find("HistoryGrid", true).FirstOrDefault() as DataGridView;

                if (resultTextBox != null)
                {
                    resultTextBox.Text = "正在执行配料优化计算...\n";
                }

                if (systemStatusText != null)
                {
                    systemStatusText.Text = "系统状态: 计算中...\n算法服务: 计算中\n数据库: 连接正常\n最后更新: " + DateTime.Now.ToString("HH:mm:ss");
                }

                // 获取输入参数
                var tfeTarget = decimal.Parse(Controls.Find("TFeTarget", true).FirstOrDefault()?.Text ?? "55.0");
                var basicityTarget = decimal.Parse(Controls.Find("BasicityTarget", true).FirstOrDefault()?.Text ?? "1.90");
                var mgoTarget = decimal.Parse(Controls.Find("MgOTarget", true).FirstOrDefault()?.Text ?? "2.39");
                var al2o3Target = decimal.Parse(Controls.Find("Al2O3Target", true).FirstOrDefault()?.Text ?? "1.89");

                // 创建完整的输入数据
                var input = new PythonAlgorithmInput
                {
                    MaterialNames = new List<string> { "印粉海娜", "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "生石灰", "轻烧白云石", "焦粉", "澳粉纵横" },
                    MaterialsMatrix = new decimal[,]
                    {
                        {63.66m, 0.10m, 4.01m, 0.24m, 2.42m, 6.70m, 1.60m, 832.98m},
                        {62.95m, 1.71m, 4.61m, 3.70m, 2.29m, 10.00m, -0.35m, 772.21m},
                        {55.54m, 10.60m, 5.59m, 2.34m, 2.09m, 0.50m, 1.73m, 550.00m},
                        {56.16m, 6.56m, 6.31m, 2.39m, 2.51m, 10.73m, 1.74m, 100.00m},
                        {26.46m, 28.15m, 15.43m, 2.79m, 2.53m, 7.60m, 12.05m, 550.00m},
                        {0.00m, 71.74m, 3.52m, 2.28m, 1.19m, 7.00m, 16.33m, 219.00m},
                        {0.00m, 42.67m, 5.31m, 26.12m, 0.10m, 1.50m, 19.73m, 183.76m},
                        {0.19m, 0.37m, 8.82m, 0.22m, 3.31m, 13.15m, 79.40m, 520.00m},
                        {60.80m, 0.10m, 4.35m, 0.20m, 2.30m, 8.30m, 6.89m, 832.98m}
                    },
                    ManualSelection = new[] { true, true, true, true, true, true, true, true, true },
                    Targets = new Dictionary<string, decimal>
                    {
                        ["TFe"] = tfeTarget,
                        ["R"] = basicityTarget,
                        ["MgO"] = mgoTarget,
                        ["Al2O3"] = al2o3Target
                    },
                    Ranges = new Dictionary<string, decimal[]>
                    {
                        ["TFe"] = new[] { tfeTarget - 1.5m, tfeTarget + 1.5m },
                        ["R"] = new[] { basicityTarget - 0.15m, basicityTarget + 0.15m },
                        ["MgO"] = new[] { 1.8m, 3.0m },
                        ["Al2O3"] = new[] { 1.5m, 2.5m },
                        ["Cost"] = new[] { 600m, 665m }
                    },
                    Weights = new Dictionary<string, decimal>
                    {
                        ["TFe"] = 0.5m,
                        ["R"] = 0.3m,
                        ["MgO"] = 0.1m,
                        ["Al2O3"] = 0.1m
                    },
                    InitialRatios = new[] { 21.0m, 20.0m, 25.0m, 7.0m, 4.0m, 6.0m, 4.0m, 4.0m, 9.0m },
                    BoundsMatrix = new decimal[,]
                    {
                        {15m, 25m}, {15m, 25m}, {20m, 30m}, {5m, 10m}, {2m, 6m},
                        {5m, 8m}, {2m, 5m}, {3m, 5m}, {8m, 15m}
                    },
                    MinWetRatioThreshold = 2.0m,
                    MaxIterations = 500,
                    Tolerance = 1e-7m
                };

                var result = await _pythonService.RunSQPOptimizationAsync(input);

                // 更新结果显示
                if (resultTextBox != null)
                {
                    if (result.IsSuccess)
                    {
                        resultTextBox.Text = $"✅ 优化计算完成!\n\n" +
                                           $"📊 计算统计:\n" +
                                           $"  • 计算时间: {result.CalculationTime}ms\n" +
                                           $"  • 迭代次数: {result.IterationCount}\n" +
                                           $"  • 目标函数值: {result.ObjectiveValue:F6}\n\n" +
                                           $"🎯 最优配比方案:\n" +
                                           string.Join("\n", result.OptimalRatios.Select((r, i) => $"  • {input.MaterialNames[i]}: {r:F2}%")) +
                                           $"\n\n📈 预测成分:\n" +
                                           string.Join("\n", result.FinalProperties.Select(p => $"  • {p.Key}: {p.Value:F2}")) +
                                           $"\n\n⏰ 完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                        // 更新配比表格
                        if (ratioGrid != null && result.OptimalRatios.Length > 0)
                        {
                            for (int i = 0; i < Math.Min(ratioGrid.Rows.Count, result.OptimalRatios.Length); i++)
                            {
                                ratioGrid.Rows[i].Cells["OptimalRatio"].Value = result.OptimalRatios[i].ToString("F2");
                                // 计算下料量（假设总产量100t/h）
                                var feedRate = result.OptimalRatios[i] * 100 / 100;
                                ratioGrid.Rows[i].Cells["FeedRate"].Value = feedRate.ToString("F2");
                            }
                        }

                        // 添加到历史记录
                        if (historyGrid != null)
                        {
                            historyGrid.Rows.Insert(0, new object[]
                            {
                                DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                tfeTarget.ToString("F2"),
                                basicityTarget.ToString("F2"),
                                "成功",
                                result.CalculationTime.ToString()
                            });
                        }
                    }
                    else
                    {
                        resultTextBox.Text = $"❌ 优化计算失败!\n\n" +
                                           $"🚫 错误信息: {result.ErrorMessage}\n" +
                                           $"⏱️ 计算时间: {result.CalculationTime}ms\n\n" +
                                           $"💡 建议检查:\n" +
                                           $"  • API服务是否正常运行\n" +
                                           $"  • 目标参数是否合理\n" +
                                           $"  • 网络连接是否正常\n\n" +
                                           $"⏰ 失败时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                        // 添加失败记录到历史
                        if (historyGrid != null)
                        {
                            historyGrid.Rows.Insert(0, new object[]
                            {
                                DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                tfeTarget.ToString("F2"),
                                basicityTarget.ToString("F2"),
                                "失败",
                                result.CalculationTime.ToString()
                            });
                        }
                    }
                }

                // 更新系统状态
                if (systemStatusText != null)
                {
                    systemStatusText.Text = $"系统状态: {(result.IsSuccess ? "计算完成" : "计算失败")}\n" +
                                          $"算法服务: {(result.IsSuccess ? "正常" : "异常")}\n" +
                                          $"数据库: 连接正常\n" +
                                          $"最后更新: {DateTime.Now.ToString("HH:mm:ss")}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"执行优化计算时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                var systemStatusText = Controls.Find("SystemStatusText", true).FirstOrDefault() as TextBox;
                if (systemStatusText != null)
                {
                    systemStatusText.Text = "系统状态: 错误\n算法服务: 异常\n数据库: 连接正常\n最后更新: " + DateTime.Now.ToString("HH:mm:ss");
                }
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // 刷新监控数据
                var qualityText = Controls.Find("QualityMonitorText", true).FirstOrDefault() as TextBox;
                var productionText = Controls.Find("ProductionMonitorText", true).FirstOrDefault() as TextBox;

                if (qualityText != null)
                {
                    // 模拟实时数据
                    var random = new Random();
                    qualityText.Text = $"TFe: {(55.0 + random.NextDouble() * 2 - 1):F2} %\n" +
                                     $"碱度: {(1.90 + random.NextDouble() * 0.2 - 0.1):F2}\n" +
                                     $"MgO: {(2.39 + random.NextDouble() * 0.4 - 0.2):F2} %\n" +
                                     $"Al2O3: {(1.89 + random.NextDouble() * 0.3 - 0.15):F2} %\n" +
                                     $"转鼓指数: {(75.0 + random.NextDouble() * 10):F1} %";
                }

                if (productionText != null)
                {
                    var random = new Random();
                    productionText.Text = $"产量: {(95 + random.Next(10)):F0} t/h\n" +
                                        $"配料速度: {(98 + random.Next(5)):F0} t/h\n" +
                                        $"混匀矿仓位: {random.Next(1, 9)}\n" +
                                        $"下料异常: {(random.Next(10) > 8 ? "仓位3异常" : "无")}";
                }

                MessageBox.Show("数据刷新完成", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"刷新数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void StopButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("停止计算功能开发中...", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ConfirmButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var ratioGrid = Controls.Find("MaterialRatioGrid", true).FirstOrDefault() as DataGridView;
                if (ratioGrid != null)
                {
                    // 将优化配比复制到当前配比
                    foreach (DataGridViewRow row in ratioGrid.Rows)
                    {
                        if (row.Cells["OptimalRatio"].Value != null)
                        {
                            row.Cells["CurrentRatio"].Value = row.Cells["OptimalRatio"].Value;
                        }
                    }
                    MessageBox.Show("配比方案已确认并应用到当前配比", "确认成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"确认配比时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("保存方案功能开发中...\n将支持保存到数据库和文件", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("加载方案功能开发中...\n将支持从数据库和文件加载", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("导出报告功能开发中...\n将支持导出Excel和PDF格式", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    /// <summary>
    /// 料流跟踪窗体
    /// </summary>
    public partial class MaterialFlowTrackingForm : Form
    {
        private readonly ILogger<MaterialFlowTrackingForm> _logger;
        private readonly IMaterialFlowService _flowService;

        public MaterialFlowTrackingForm(
            ILogger<MaterialFlowTrackingForm> logger,
            IMaterialFlowService flowService)
        {
            _logger = logger;
            _flowService = flowService;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Text = "料流跟踪";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterParent;

            var label = new Label
            {
                Text = "料流跟踪功能开发中...\n\n" +
                       "将包含以下功能：\n" +
                       "• 灌仓管理\n" +
                       "• 下料批次跟踪\n" +
                       "• 动态定位\n" +
                       "• 成分匹配\n" +
                       "• 异常处理",
                Font = new Font("Microsoft YaHei", 12F),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            Controls.Add(label);
        }
    }

    /// <summary>
    /// 预测分析窗体
    /// </summary>
    public partial class PredictionAnalysisForm : Form
    {
        private readonly ILogger<PredictionAnalysisForm> _logger;

        public PredictionAnalysisForm(ILogger<PredictionAnalysisForm> logger)
        {
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Text = "预测分析";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterParent;

            var label = new Label
            {
                Text = "预测分析功能开发中...\n\n" +
                       "将包含以下功能：\n" +
                       "• 烧结矿成分预测\n" +
                       "• 质量指标预测\n" +
                       "• 成本预测\n" +
                       "• 趋势分析\n" +
                       "• 神经网络模型",
                Font = new Font("Microsoft YaHei", 12F),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            Controls.Add(label);
        }
    }

    /// <summary>
    /// 系统配置窗体
    /// </summary>
    public partial class SystemConfigurationForm : Form
    {
        private readonly ILogger<SystemConfigurationForm> _logger;
        private readonly IConfigurationService _configService;

        public SystemConfigurationForm(
            ILogger<SystemConfigurationForm> logger,
            IConfigurationService configService)
        {
            _logger = logger;
            _configService = configService;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Text = "系统配置";
            Size = new Size(600, 500);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };

            // Python配置页
            var pythonTab = new TabPage("Python配置");
            var pythonLabel = new Label
            {
                Text = "Python环境配置\n\n" +
                       "• Python路径配置\n" +
                       "• 脚本路径配置\n" +
                       "• 依赖包管理\n" +
                       "• 环境检测",
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill
            };
            pythonTab.Controls.Add(pythonLabel);

            // 算法配置页
            var algorithmTab = new TabPage("算法配置");
            var algorithmLabel = new Label
            {
                Text = "优化算法配置\n\n" +
                       "• 算法参数设置\n" +
                       "• 约束条件配置\n" +
                       "• 权重设置\n" +
                       "• 缓存配置",
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill
            };
            algorithmTab.Controls.Add(algorithmLabel);

            // 数据库配置页
            var databaseTab = new TabPage("数据库配置");
            var databaseLabel = new Label
            {
                Text = "数据库连接配置\n\n" +
                       "• 连接字符串设置\n" +
                       "• 连接池配置\n" +
                       "• 备份设置\n" +
                       "• 性能监控",
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill
            };
            databaseTab.Controls.Add(databaseLabel);

            tabControl.TabPages.AddRange(new TabPage[] { pythonTab, algorithmTab, databaseTab });

            var buttonPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom
            };

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                Location = new Point(440, 8),
                Size = new Size(75, 23)
            };

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                Location = new Point(520, 8),
                Size = new Size(75, 23)
            };

            buttonPanel.Controls.AddRange(new Control[] { okButton, cancelButton });
            Controls.AddRange(new Control[] { tabControl, buttonPanel });
        }
    }
}
