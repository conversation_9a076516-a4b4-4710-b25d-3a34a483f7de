{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=SinterOptimizationDB;Trusted_Connection=true;TrustServerCertificate=true;", "RedisConnection": "localhost:6379"}, "Python": {"ApiBaseUrl": "http://localhost:5000", "Timeout": 300, "MaxRetries": 3, "ExecutablePath": "C:\\Anaconda3\\envs\\deeplearning\\python.exe", "ScriptsPath": "py", "Environment": {"PYTHONPATH": "./py", "PYTHONIOENCODING": "utf-8"}}, "Algorithm": {"DefaultAlgorithm": "SQP", "SQP": {"MaxIterations": 500, "Tolerance": 1e-07, "MinWetRatioThreshold": 2.0}, "Cache": {"Enabled": true, "ExpirationMinutes": 60, "MaxCacheSize": 1000}}, "BlendingCalculation": {"DefaultMode": "Automatic", "Constraints": {"TFe": {"Min": 53.5, "Max": 56.5, "Target": 55.0}, "Basicity": {"Min": 1.75, "Max": 2.05, "Target": 1.9}, "MgO": {"Min": 1.8, "Max": 3.0, "Target": 2.39}, "Al2O3": {"Min": 1.5, "Max": 2.5, "Target": 1.89}, "Cost": {"Min": 600, "Max": 665}}, "Weights": {"TFe": 0.5, "Basicity": 0.3, "MgO": 0.1, "Al2O3": 0.1}}, "MaterialManagement": {"AutoRefreshInterval": 300, "LowStockThreshold": 0.2, "CompositionValidation": {"MaxDeviation": 5.0, "ConfidenceThreshold": 95.0}}, "Monitoring": {"PerformanceLogging": true, "AlertThresholds": {"CalculationTime": 30000, "ErrorRate": 0.05, "MemoryUsage": 0.8}}, "UI": {"Theme": "<PERSON><PERSON><PERSON>", "Language": "zh-CN", "AutoSave": true, "AutoSaveInterval": 60, "ChartRefreshInterval": 5}, "Security": {"EnableAuditLog": true, "SessionTimeout": 480, "PasswordPolicy": {"MinLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialChar": false}}, "Integration": {"LabSystem": {"Enabled": false, "ApiUrl": "http://localhost:8080/api", "ApiKey": "", "SyncInterval": 600}, "PLCSystem": {"Enabled": false, "ConnectionString": "*************:502", "ReadInterval": 1000}, "MESSystem": {"Enabled": false, "ApiUrl": "http://localhost:9090/api", "ApiKey": ""}}}