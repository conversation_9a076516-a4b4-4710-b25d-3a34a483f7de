Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SinterOptimizationSystem", "src\SinterOptimizationSystem\SinterOptimizationSystem.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SinterOptimizationSystem.Core", "src\SinterOptimizationSystem.Core\SinterOptimizationSystem.Core.csproj", "{3B749309-F4A4-4085-B2BF-E6906AC980D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SinterOptimizationSystem.Services", "src\SinterOptimizationSystem.Services\SinterOptimizationSystem.Services.csproj", "{CA5A1E3D-E82B-4DFA-B289-A8B2C8E1CB1B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SinterOptimizationSystem.Tests", "tests\SinterOptimizationSystem.Tests\SinterOptimizationSystem.Tests.csproj", "{D89DA204-C80E-44C9-AA5B-31FAD6556134}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B749309-F4A4-4085-B2BF-E6906AC980D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B749309-F4A4-4085-B2BF-E6906AC980D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B749309-F4A4-4085-B2BF-E6906AC980D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B749309-F4A4-4085-B2BF-E6906AC980D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA5A1E3D-E82B-4DFA-B289-A8B2C8E1CB1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA5A1E3D-E82B-4DFA-B289-A8B2C8E1CB1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA5A1E3D-E82B-4DFA-B289-A8B2C8E1CB1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA5A1E3D-E82B-4DFA-B289-A8B2C8E1CB1B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D89DA204-C80E-44C9-AA5B-31FAD6556134}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D89DA204-C80E-44C9-AA5B-31FAD6556134}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D89DA204-C80E-44C9-AA5B-31FAD6556134}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D89DA204-C80E-44C9-AA5B-31FAD6556134}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9ABC-DEF123456789}
	EndGlobalSection
EndGlobal
