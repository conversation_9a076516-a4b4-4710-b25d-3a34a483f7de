using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;

namespace SinterOptimizationSystem
{
    /// <summary>
    /// 主窗体
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly ILogger<MainForm> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IApplicationService _applicationService;
        private readonly IConfigurationService _configurationService;

        // 子窗体
        private MaterialManagementForm? _materialManagementForm;
        private BlendingCalculationForm? _blendingCalculationForm;
        private MaterialFlowTrackingForm? _materialFlowTrackingForm;
        private PredictionAnalysisForm? _predictionAnalysisForm;
        private SystemConfigurationForm? _systemConfigurationForm;

        // 控件
        private MenuStrip _mainMenuStrip;
        private StatusStrip _statusStrip;
        private ToolStripStatusLabel _statusLabel;
        private ToolStripStatusLabel _timeLabel;
        private Panel _mainPanel;
        private System.Windows.Forms.Timer _statusTimer = new System.Windows.Forms.Timer();

        public MainForm(
            ILogger<MainForm> logger,
            IServiceProvider serviceProvider,
            IApplicationService applicationService,
            IConfigurationService configurationService)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _applicationService = applicationService;
            _configurationService = configurationService;

            InitializeComponent();
            InitializeAsync();
        }

        private void InitializeComponent()
        {
            // 设置窗体属性
            Text = "智能优化配料系统 v1.0";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            Icon = SystemIcons.Application;

            // 创建主菜单
            CreateMainMenu();

            // 创建状态栏
            CreateStatusBar();

            // 创建主面板
            CreateMainPanel();

            // 创建定时器
            _statusTimer = new System.Windows.Forms.Timer();
            _statusTimer.Interval = 1000; // 1秒更新一次
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();
        }

        private void CreateMainMenu()
        {
            _mainMenuStrip = new MenuStrip();

            // 系统菜单
            var systemMenu = new ToolStripMenuItem("系统(&S)");
            systemMenu.DropDownItems.Add(new ToolStripMenuItem("系统配置", null, SystemConfiguration_Click));
            systemMenu.DropDownItems.Add(new ToolStripSeparator());
            systemMenu.DropDownItems.Add(new ToolStripMenuItem("退出", null, Exit_Click));

            // 原料管理菜单
            var materialMenu = new ToolStripMenuItem("原料管理(&M)");
            materialMenu.DropDownItems.Add(new ToolStripMenuItem("原料信息管理", null, MaterialManagement_Click));
            materialMenu.DropDownItems.Add(new ToolStripMenuItem("化学成分管理", null, CompositionManagement_Click));
            materialMenu.DropDownItems.Add(new ToolStripMenuItem("物理性能管理", null, PhysicalPropertyManagement_Click));
            materialMenu.DropDownItems.Add(new ToolStripMenuItem("性价比分析", null, CostPerformanceAnalysis_Click));

            // 配料计算菜单
            var blendingMenu = new ToolStripMenuItem("配料计算(&B)");
            blendingMenu.DropDownItems.Add(new ToolStripMenuItem("配料方案设计", null, BlendingSchemeDesign_Click));
            blendingMenu.DropDownItems.Add(new ToolStripMenuItem("配料计算", null, BlendingCalculation_Click));
            blendingMenu.DropDownItems.Add(new ToolStripMenuItem("下料量分配", null, FeedingAllocation_Click));
            blendingMenu.DropDownItems.Add(new ToolStripMenuItem("实时调整", null, RealTimeAdjustment_Click));

            // 料流跟踪菜单
            var flowMenu = new ToolStripMenuItem("料流跟踪(&F)");
            flowMenu.DropDownItems.Add(new ToolStripMenuItem("料流跟踪", null, MaterialFlowTracking_Click));
            flowMenu.DropDownItems.Add(new ToolStripMenuItem("灌仓管理", null, BinFillingManagement_Click));
            flowMenu.DropDownItems.Add(new ToolStripMenuItem("下料批次跟踪", null, FeedingBatchTracking_Click));
            flowMenu.DropDownItems.Add(new ToolStripMenuItem("动态定位", null, DynamicPositioning_Click));

            // 预测分析菜单
            var predictionMenu = new ToolStripMenuItem("预测分析(&P)");
            predictionMenu.DropDownItems.Add(new ToolStripMenuItem("烧结矿成分预测", null, SinterCompositionPrediction_Click));
            predictionMenu.DropDownItems.Add(new ToolStripMenuItem("质量指标预测", null, QualityIndexPrediction_Click));
            predictionMenu.DropDownItems.Add(new ToolStripMenuItem("成本预测", null, CostPrediction_Click));
            predictionMenu.DropDownItems.Add(new ToolStripMenuItem("趋势分析", null, TrendAnalysis_Click));

            // 报表菜单
            var reportMenu = new ToolStripMenuItem("报表(&R)");
            reportMenu.DropDownItems.Add(new ToolStripMenuItem("配料报表", null, BlendingReport_Click));
            reportMenu.DropDownItems.Add(new ToolStripMenuItem("料流报表", null, FlowReport_Click));
            reportMenu.DropDownItems.Add(new ToolStripMenuItem("质量报表", null, QualityReport_Click));
            reportMenu.DropDownItems.Add(new ToolStripMenuItem("成本报表", null, CostReport_Click));

            // 帮助菜单
            var helpMenu = new ToolStripMenuItem("帮助(&H)");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("用户手册", null, UserManual_Click));
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("关于", null, About_Click));

            _mainMenuStrip.Items.AddRange(new ToolStripItem[]
            {
                systemMenu, materialMenu, blendingMenu, flowMenu, predictionMenu, reportMenu, helpMenu
            });

            Controls.Add(_mainMenuStrip);
            MainMenuStrip = _mainMenuStrip;
        }

        private void CreateStatusBar()
        {
            _statusStrip = new StatusStrip();

            _statusLabel = new ToolStripStatusLabel("就绪")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            _statusStrip.Items.AddRange(new ToolStripItem[] { _statusLabel, _timeLabel });
            Controls.Add(_statusStrip);
        }

        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.LightGray
            };

            // 添加欢迎信息
            var welcomeLabel = new Label
            {
                Text = "欢迎使用智能优化配料系统\n\n" +
                       "主要功能模块：\n" +
                       "• 原料及成品矿成分管理\n" +
                       "• 配料计算与优化\n" +
                       "• 料流跟踪与动态定位\n" +
                       "• 烧结矿成分预测\n" +
                       "• 返矿配比调整",
                Font = new Font("Microsoft YaHei", 12F, FontStyle.Regular),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            _mainPanel.Controls.Add(welcomeLabel);
            Controls.Add(_mainPanel);
        }

        private async void InitializeAsync()
        {
            try
            {
                _statusLabel.Text = "正在初始化系统...";
                await _applicationService.InitializeAsync();
                await _configurationService.LoadAsync();
                _statusLabel.Text = "系统初始化完成，就绪";
                _logger.LogInformation("主窗体初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "主窗体初始化失败");
                _statusLabel.Text = "系统初始化失败";
                MessageBox.Show($"系统初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region 事件处理

        private void StatusTimer_Tick(object? sender, EventArgs e)
        {
            _timeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        private void SystemConfiguration_Click(object? sender, EventArgs e)
        {
            ShowSystemConfigurationForm();
        }

        private void Exit_Click(object? sender, EventArgs e)
        {
            Close();
        }

        private void MaterialManagement_Click(object? sender, EventArgs e)
        {
            ShowMaterialManagementForm();
        }

        private void CompositionManagement_Click(object? sender, EventArgs e)
        {
            ShowMaterialManagementForm(); // 可以传递参数指定显示成分管理页面
        }

        private void PhysicalPropertyManagement_Click(object? sender, EventArgs e)
        {
            ShowMaterialManagementForm(); // 可以传递参数指定显示物理性能管理页面
        }

        private void CostPerformanceAnalysis_Click(object? sender, EventArgs e)
        {
            ShowMaterialManagementForm(); // 可以传递参数指定显示性价比分析页面
        }

        private void BlendingSchemeDesign_Click(object? sender, EventArgs e)
        {
            ShowBlendingCalculationForm();
        }

        private void BlendingCalculation_Click(object? sender, EventArgs e)
        {
            ShowBlendingCalculationForm();
        }

        private void FeedingAllocation_Click(object? sender, EventArgs e)
        {
            ShowBlendingCalculationForm(); // 可以传递参数指定显示下料量分配页面
        }

        private void RealTimeAdjustment_Click(object? sender, EventArgs e)
        {
            ShowBlendingCalculationForm(); // 可以传递参数指定显示实时调整页面
        }

        private void MaterialFlowTracking_Click(object? sender, EventArgs e)
        {
            ShowMaterialFlowTrackingForm();
        }

        private void BinFillingManagement_Click(object? sender, EventArgs e)
        {
            ShowMaterialFlowTrackingForm(); // 可以传递参数指定显示灌仓管理页面
        }

        private void FeedingBatchTracking_Click(object? sender, EventArgs e)
        {
            ShowMaterialFlowTrackingForm(); // 可以传递参数指定显示下料批次跟踪页面
        }

        private void DynamicPositioning_Click(object? sender, EventArgs e)
        {
            ShowMaterialFlowTrackingForm(); // 可以传递参数指定显示动态定位页面
        }

        private void SinterCompositionPrediction_Click(object? sender, EventArgs e)
        {
            ShowPredictionAnalysisForm();
        }

        private void QualityIndexPrediction_Click(object? sender, EventArgs e)
        {
            ShowPredictionAnalysisForm(); // 可以传递参数指定显示质量指标预测页面
        }

        private void CostPrediction_Click(object? sender, EventArgs e)
        {
            ShowPredictionAnalysisForm(); // 可以传递参数指定显示成本预测页面
        }

        private void TrendAnalysis_Click(object? sender, EventArgs e)
        {
            ShowPredictionAnalysisForm(); // 可以传递参数指定显示趋势分析页面
        }

        private void BlendingReport_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("配料报表功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void FlowReport_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("料流报表功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void QualityReport_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("质量报表功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CostReport_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("成本报表功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UserManual_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("用户手册功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void About_Click(object? sender, EventArgs e)
        {
            var appInfo = _applicationService.GetApplicationInfo();
            MessageBox.Show($"{appInfo.Name}\n版本: {appInfo.Version}\n公司: {appInfo.Company}\n启动时间: {appInfo.StartTime:yyyy-MM-dd HH:mm:ss}",
                "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region 窗体显示方法

        private void ShowMaterialManagementForm()
        {
            if (_materialManagementForm == null || _materialManagementForm.IsDisposed)
            {
                _materialManagementForm = _serviceProvider.GetRequiredService<MaterialManagementForm>();
            }
            _materialManagementForm.Show();
            _materialManagementForm.BringToFront();
        }

        private void ShowBlendingCalculationForm()
        {
            if (_blendingCalculationForm == null || _blendingCalculationForm.IsDisposed)
            {
                _blendingCalculationForm = _serviceProvider.GetRequiredService<BlendingCalculationForm>();
            }
            _blendingCalculationForm.Show();
            _blendingCalculationForm.BringToFront();
        }

        private void ShowMaterialFlowTrackingForm()
        {
            if (_materialFlowTrackingForm == null || _materialFlowTrackingForm.IsDisposed)
            {
                _materialFlowTrackingForm = _serviceProvider.GetRequiredService<MaterialFlowTrackingForm>();
            }
            _materialFlowTrackingForm.Show();
            _materialFlowTrackingForm.BringToFront();
        }

        private void ShowPredictionAnalysisForm()
        {
            if (_predictionAnalysisForm == null || _predictionAnalysisForm.IsDisposed)
            {
                _predictionAnalysisForm = _serviceProvider.GetRequiredService<PredictionAnalysisForm>();
            }
            _predictionAnalysisForm.Show();
            _predictionAnalysisForm.BringToFront();
        }

        private void ShowSystemConfigurationForm()
        {
            if (_systemConfigurationForm == null || _systemConfigurationForm.IsDisposed)
            {
                _systemConfigurationForm = _serviceProvider.GetRequiredService<SystemConfigurationForm>();
            }
            _systemConfigurationForm.ShowDialog();
        }

        #endregion

        protected override async void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                _statusLabel.Text = "正在关闭系统...";
                await _configurationService.SaveAsync();
                await _applicationService.ShutdownAsync();
                _logger.LogInformation("应用程序正常关闭");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用程序关闭异常");
            }

            base.OnFormClosing(e);
        }
    }
}
